#ifndef __N50_DMS_PROCESS_H__
#define __N50_DMS_PROCESS_H__
#include <functional>
#include <memory>
#include <opencv2/imgproc.hpp>
#include "cc_math_tool.h"
#include "cc_status_hold.h"
#include "dms_out_warning.h"
#include "dms_warming.h"
#include "occlusion_detector.h"
#include "tx_dms_sdk.h"

namespace tongxing {

// 前向声明
class CcDmsProcess;
struct KeypointsStabilityAnalysisResult;

typedef enum InternalCameraType_ {
    Camera_Norm = 1 << 0,     // 正常状态 (0b00001)
    Camera_Occ = 1 << 1,      // 相机遮挡 (0b00010)
    No_face = 1 << 2,         // 无人脸 (0b00100)
    No_face_mask = 1 << 3,    // 戴口罩 (0b01000)
    No_face_irblock = 1 << 4  // 红外阻断 (0b10000)
} InternalCameraType;

class DmsProcess {
    typedef std::function<std::shared_ptr<CcObjBBox>(void)> GetBboxFun;
    typedef std::function<std::shared_ptr<std::vector<CcObjBBox>>(void)> GetBboxArrayFun;
    typedef std::function<std::shared_ptr<std::vector<cv::Point2f>>(void)> GetPointArrayFun;
    typedef std::function<std::shared_ptr<std::vector<std::vector<cv::Point>>>(void)>
        GetPointArrayArrayFun;
    typedef std::function<std::shared_ptr<std::vector<float>>(void)> GetFloatArrayFun;
    typedef std::function<bool(void)> GetBoolFun;
    typedef std::function<int(void)> GetIntFun;
    typedef std::function<std::shared_ptr<KeypointsStabilityAnalysisResult>(void)> GetStabilityAnalysisFun;

  public:
    int Init(const GetBboxFun& get_driving_face_bbox,
             const GetPointArrayFun& get_driving_face_keypoint,
             const GetFloatArrayFun& get_driving_face_angle,
             const GetPointArrayFun& get_driving_eye_keypoint,
             const GetFloatArrayFun& get_driving_right_eye_close_score,
             const GetFloatArrayFun& get_driving_left_eye_close_score,
             const GetIntFun& get_occlusion,
             const GetFloatArrayFun& get_driving_face_attr,
             const GetFloatArrayFun& get_driving_right_eye_landmarks,
             const GetFloatArrayFun& get_driving_left_eye_landmarks,
             const GetFloatArrayFun& get_exact_lum_info,
             const GetBboxArrayFun& get_phone_cig_bbox,
             const GetStabilityAnalysisFun& get_keypoints_stability_analysis,
             const char* config_file);
    int execute(const TXCarInfo* car_info,
                long long frame_id,
                TXDmsResult& result_out,
                long ts = 0);
    int SetFatigue(bool type);
    bool GetFatigue();
    int DrowsiRestAlarm();
    int DmsAlarmSetOk();
    int DmsDistractRestAlarm();
    std::string GetDistractReason(TXCameraStatus camera_status);
    std::string GetDistractParamers();
    int SetPhoneAndSmokingDetectEnableStatus(unsigned char flag);

    void GetTiredInfo(tx_tired& tired_info);
    int GetRightLeftEyeThr(float& left_eye_thr, float& right_eye_thr);
    void GetDistractionInfo(internal_analysis_distraction_info& info);
    void getNonDistractHeadRotation(float& headyaw_min,
                                    float& headyaw_max,
                                    float& headpitch_min,
                                    float& headpitch_max);

  private:
    cc_dms_warming noface_warning;
    cc_dms_warming occlusion_warning;
    CcBoolTimeWindowsStatistics phone_det_win;
    CcBoolTimeWindowsStatistics smoking_det_win;
    // cc_dms_warming Mask_warning;     //戴口罩
    // cc_dms_warming Glasses_warning;  //戴眼镜
    // CcStatusHold yawn_status_hold;
    bool enable_fatigue_ = true;

    bool is_first_face_occ = false;  //人脸遮挡标注位（上电过程中，只报警一次，其余报正常）

    //获取左右眼面积占比
    float leye_thr;
    float reye_thr;

    InternalCameraType last_camera_history_warn_type = Camera_Norm;

    long last_ts = 0;                //保存上一帧时间戳
    long last_frame_control_ts = 0;  //帧率控制，上一帧时间戳
    bool is_clear_eyeframes = false;
    unsigned int activateNum = 36000 * 2;  //未激活试用帧数

    // 低速帧计数器和状态管理优化
    static constexpr int kLowSpeedResetFrameCount = 5;  // 连续低速帧数阈值
    int low_speed_frame_count_ = 0;                     // 连续低速帧计数器
    bool low_speed_reset_triggered_ = false;            // 低速重置是否已触发

    static constexpr float kFaceBboxThr = 0.5f;          // 人脸框阈值
    static constexpr float kFaceAngleThreshold = 0.35f;  // 人脸关键点阈值
    static constexpr float kMinFaceAttrThr = 0.7f;  // 人脸属性阈值(ir_blocked,masked,glassed...)
    static constexpr float kMinEyeOpening = 0.10f;    // 睁眼阈值
    static constexpr float kMouthOpeningThr = 0.6f;  // 嘴巴张合阈值
    static constexpr int kMinEyeXPx = 32;            // 有效眼睛横坐标最小像素数
    static constexpr int kMinFaceHeightPx = 230;     // 有效人脸高度最小像素数
    int kFatigueSpeedThr = 10;                       // 疲劳报警触发速度阈值
    int kDistraSpeedThr = 20;                        // 分心报警触发速度阈值
    static constexpr int kFaceOccTriggerSpeed = 10;       // 人脸遮挡触发速度
    bool is_face_valid = false;                      // 是否为有效的人脸
    bool is_face_keypoints_valid = false;            // 是否为有效的人脸关键点
    float face_angle_score = 0;
    float mouthpoint_score = 0;
    
    static constexpr int kMinFaceBlockedEnterTimeMs = 10000; // 进入人脸遮挡的时间。实际是9s，但逻辑处理时使用了比例，让其符合了9s
    static constexpr int kMinFaceBlockedExitTimeMs = 2000; // 退出人脸遮挡的时间

    distra_related_eyeinfo current_distra_related_eyeinfo;

  private:
    unsigned char phone_smoking_detect_enable_status_;
    GetBboxFun get_driving_face_bbox_;
    GetPointArrayFun get_driving_face_keypoint_;
    GetFloatArrayFun get_driving_face_angle_;
    GetPointArrayFun get_driving_eye_keypoint_;
    GetFloatArrayFun get_driving_right_eye_close_score_;
    GetFloatArrayFun get_driving_left_eye_close_score_;
    GetFloatArrayFun get_driving_right_eye_landmarks_;
    GetFloatArrayFun get_driving_left_eye_landmarks_;
    GetIntFun get_occlusion_;
    GetFloatArrayFun get_driving_face_attr_;
    GetFloatArrayFun get_exact_lum_info_;
    GetBboxArrayFun get_phone_cig_bbox_;
    GetStabilityAnalysisFun get_keypoints_stability_analysis_;
    std::shared_ptr<dms_out_warning> tx_dms_out_warning;

    void processKeypointsForDistraction(TXDmsFaceInfo& face_info,
                                       distra_related_eyeinfo& related_eyeinfo,
                                       long now_ts);

    bool isAlgoActivate();
    bool isHardwareError(const TXCarInfo* car_info, TXDmsResult& result_out);
    void phoneAndSmokingProcess(TXDmsResult& result, const long& now_ts);
    void parseFaceBboxInfo(TXDmsFaceInfo& face_info);
    bool isFaceValid();
    bool isFaceKeypointsValid();
    void parseFaceAngle(TXDmsFaceInfo& face_info, float& face_angle_score, float& mouthpoint_score);
    void parseFaceAttr(TXDmsFaceInfo& face_info);
    void isEyeLumAbnormal(TXDmsFaceInfo& face_info, std::shared_ptr<std::vector<float>> lum_info_ptr);
    void parseFaceKeypoints(TXDmsFaceInfo& face_info,
                            std::vector<cv::Point>& mouth_point,
                            bool& is_leye_x_px_valid,
                            bool& is_reye_x_px_valid);
    void checkFatigueWithEye(TXEyeLandmark& eye_result, float& fatigue_score);
    void checkFatigueWithMouth(const std::vector<cv::Point>& mouth_points, float head_pitch_diff, float& fatigue_score);

    u_int8_t getCurCamStatus(TXDmsFaceInfo& face_info);
    TXCameraStatus getWindowCamStatus(const TXCarInfo* car_info,
                                      const long& now_ts,
                                      uint8_t camera_states);
    TXDistractionType distraProcess(const TXCarInfo* car_info,
                                    TXDmsFaceInfo& face_info,
                                    const distra_related_eyeinfo& related_eyeinfo,
                                    const bool& current_camera_occlusion,
                                    long& now_ts);
    TXDrowsinessType fatigueProcess(const TXCarInfo* car_info,
                                    TXDmsFaceInfo& face_info,
                                    TXModelOut& current_model_out,
                                    long& now_ts);
    void parseFaceProcess(TXDmsFaceInfo& face_info,
                          TXModelOut& current_model_out,
                          distra_related_eyeinfo& related_eyeinfo,
                          long& now_ts);
    
    // 状态管理辅助函数
    void resetWarningStates(InternalCameraType target_state);
    bool handleLowSpeedFrames(const TXCarInfo* car_info);
    void resetFaceWarningStates();
        TXCameraStatus updateCameraState(InternalCameraType current_state, 
                                    bool camera_occ, bool no_face, 
                                    bool wear_mask, bool wear_ir_block,
                                    const TXCarInfo* car_info, const long& now_ts, 
                                    bool should_reset_face_warnings = false);
};
}  // namespace tongxing

#endif
