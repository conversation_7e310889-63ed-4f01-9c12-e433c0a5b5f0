#include "dms_out_warning.h"
#include "CalmCarLog.h"

namespace tongxing {
dms_out_warning::dms_out_warning(/* args */) {
    Dwarn = new DrowsinessWarn(70, 60, 60, 60);
    Distrac = new DistractionWarn();
}

dms_out_warning::~dms_out_warning() {
    ;
}

int dms_out_warning::dms_init_warning() {
    ;
    return 0;
}

int dms_out_warning::output_model_bool(const TXModelOut* model_out,
                                       Warn_Info& warn_info_,
                                       TXDmsFaceInfo face_info) {
    /* 调整眼睛是否闭眼的判断逻辑：
1.双眼的闭眼分数都为1；
2.一只眼睛闭眼分数为1，另一只眼睛的瞳孔分数小于等于0
（包含了几种情况：
1.另一只眼睛检测不到了；
2.另一只眼睛检测到了，但是检测不到瞳孔；
3.另一只眼睛因为眼镜反射路面或者反光等因素被判定abnormal）*/
    if ((model_out->l_eye_close >= l_eye_cloes_threshold &&
         model_out->r_eye_close >= r_eye_cloes_threshold) ||
        (model_out->l_eye_close >= l_eye_cloes_threshold &&
         (face_info.right_eye_landmark.pupil_score <= 0)) ||
        (model_out->r_eye_close >= r_eye_cloes_threshold &&
         (face_info.left_eye_landmark.pupil_score <= 0))) {
        warn_info_.eye_close_status = true;
    } else {
        warn_info_.eye_close_status = false;
    }

    if (model_out->mouth_open >= MouthOpenThrUp) {
        warn_info_.mouth_open_status = 1;
    } else { // 小于0表示是程式判断模型的数据不可信时置为的一种区分状态
        if (model_out->mouth_open >= MouthOpenThrDown || model_out->mouth_open < 0) {
            warn_info_.mouth_open_status = 2;
        } else {
            warn_info_.mouth_open_status = 0;
        }
    }

    if (model_out->mouth_open >= yawn_opne_threshold) {
        //打哈欠时不能算闭眼（打哈欠时不统计闭眼）//笑时眯眼误报闭眼
        warn_info_.eye_close_status = false;
    }

    return 0;
}

TXDistractionType dms_out_warning::distrac_run_warning(const long time,
                                                       const TXCarInfo* car_info,
                                                       bool status) {
    Distraction_Info warn_info;
    warn_info.time_input = time;
    if (car_info->speed < 20 || car_info->gear != TXGearPition::FORWARD ||
        car_info->turn_light != TXTurnSignal::TURN_OFF) {
        warn_info.distraction_status = false;  //车速未达到，当前不可为分心状态
    } else {
        warn_info.distraction_status = status;
    }

    Distrac->Update(warn_info);
    DistractionType warn_type = Distrac->GetWarnStatus(car_info->speed);
    // std::cout<<"warn_info.distraction_status="<<warn_info.distraction_status<<" warn_type="<<warn_type<<" car_info->speed="<<car_info->speed<<std::endl;
    TXDistractionType warning_type;

    switch (warn_type) {
        case DISTRACTION_NORMAL:
            warning_type = Distraction_Normal;
            break;
        case DISTRACTION_FATIGUE1:
            if (car_info->speed >= 20 && (car_info->turn_light == TXTurnSignal::TURN_OFF)) {
                warning_type = Distraction_Fatigue;
            } else {
                warning_type = Distraction_Normal;
            }
            break;
        case DISTRACTION_FATIGUE2:
            if (car_info->speed >= 20 && (car_info->turn_light == TXTurnSignal::TURN_OFF)) {
                warning_type = Distraction_Fatigue_Short;
            } else {
                warning_type = Distraction_Normal;
            }
            break;

        default:
            warning_type = Distraction_Invalid;
            break;
    }

    return warning_type;
}

TXDrowsinessType dms_out_warning::drowsi_run_warning(const long time,
                                                     TXDmsFaceInfo face_info,
                                                     const TXCarInfo* car_info,
                                                     const TXModelOut* input,
                                                     TXWarnInfo& warn_info_out) {
    Warn_Info warn_info;
    //当车速不满足时，将状态都置为不成立
    if (car_info->speed < 10 || car_info->gear != TXGearPition::FORWARD) {
        warn_info.eye_close_status = false;
        warn_info.mouth_open_status = 2; // 将车机状态不满足，逻辑判断不可信，模型置信度低时置为一个中间状态
        warn_info.ldw_status = false;
        warn_info.time_input = time;
    } else {
        output_model_bool(input, warn_info, face_info);

        warn_info.time_input = time;
        warn_info.ldw_status = car_info->ldw;
    }

    // 1.先更新数据
    Dwarn->Update(warn_info);
    // 2.取更新后结果
    // 先计算轻度
    //获取结果

    WarningType warn_type = Dwarn->GetWarnStatus(warn_info_out);

    // CC_LOG_INFO("dms_run_warn", "warn_type:%d ",warn_type);
    TXDrowsinessType warning_type;

    switch (warn_type) {
        case NORMAL:
            warning_type = Drowsiness_Normal;
            break;
        case DROWSINESS_LEVEL_LIGHT:
            warning_type = Drowsiness_Fatigue2;
            break;
        case DROWSINESS_LEVEL_MEDIUM:
            warning_type = Drowsiness_Fatigue3;
            break;
        case DROWSINESS_LEVEL_HEAVEY:
            warning_type = Drowsiness_Fatigue4;
            break;
        case NO_RESPONSE:
            warning_type = Drowsiness_NoResponse;
            break;

        default:
            warning_type = Drowsiness_Invalid;
            break;
    }

    // 计算中度

    return warning_type;
}

int dms_out_warning::DrowsiRestAlarm() {
    // CC_LOG_INFO("dms_out_warning","----------DrowsiRestAlarm---------");
    Dwarn->Reset();
    return 0;
}

int dms_out_warning::DmsRestEyeAlarm() {
    // CC_LOG_INFO("dms_out_warning","----------DrowsiRestAlarm---------");
    Dwarn->Cleareye();
    return 0;
}

int dms_out_warning::DistracRestAlarm() {
    // CC_LOG_INFO("dms_out_warning","----------DrowsiRestAlarm---------");
    Distrac->Reset();
    Distrac->ResetNofaceMixdata();
    return 0;
}

int dms_out_warning::DmsAlarmSetOk() {
    // CC_LOG_INFO("dms_out_warning","----------DrowsiRestAlarm---------");
    Dwarn->SetOk();
    return 0;
}

void dms_out_warning::ResetCalibration() {
    Distrac->ClearCalibration();
    return;
}

bool dms_out_warning::GetCalibrationStatus() {
    return Distrac->GetCalibrationStatus();
}

TXDistracCaliStatus dms_out_warning::auto_calibration_distraction(TXDmsFaceInfo face_info,
                                                                  const TXCarInfo* car_info,
                                                                  const float leye_uper_curve_score,
                                                                  const float reye_uper_curve_score,
                                                                  long now_ts) {
    return Distrac->auto_calibration_distraction(face_info, car_info, leye_uper_curve_score,
                                                 reye_uper_curve_score, now_ts);
}

bool dms_out_warning::GetCurrentDistractStatus(TXDmsFaceInfo face_info,
                                               const TXCarInfo* car_info,
                                               const distra_related_eyeinfo& related_eyeinfo,
                                               bool is_face_keypoints_valid,
                                               long now_ts) {
    return Distrac->IsDistracted(face_info, car_info, related_eyeinfo, is_face_keypoints_valid,
                                 now_ts);
}

bool dms_out_warning::IsNoFaceMixDistraction(bool isFaceValid,
                                             const float face_keypoints_score,
                                             const TXCarInfo* car_info,
                                             bool camera_occlusion,
                                             bool distraction_status,
                                             long now_ts) {
    return Distrac->GetMixDistraStatus(isFaceValid, face_keypoints_score, car_info, camera_occlusion, distraction_status,
                                       now_ts);
}

void dms_out_warning::getNonDistractHeadRotation(float& headyaw_min,
                                                 float& headyaw_max,
                                                 float& headpitch_min,
                                                 float& headpitch_max) {
    return Distrac->getNonDistractHeadRotation(headyaw_min, headyaw_max, headpitch_min,
                                               headpitch_max);
}

void dms_out_warning::GetHeadPosePitch(float& min_value, float& max_value) {
    return Distrac->GetHeadPosePitch(min_value, max_value);
}

void dms_out_warning::GetHeadPoseRoll(float& min_value, float& max_value) {
    return Distrac->GetHeadPoseRoll(min_value, max_value);
}

void dms_out_warning::GetHeadPoseYaw(float& min_value, float& max_value) {
    return Distrac->GetHeadPoseYaw(min_value, max_value);
}

std::string dms_out_warning::GetDistractReason() {
    return Distrac->GetDistractReason();
}

std::string dms_out_warning::GetDistractParamers() {
    return Distrac->GetDistractParamers();
}

void dms_out_warning::GetTiredInfo(tx_tired& tired_info) {
    return Dwarn->GetTired(tired_info);
}

void dms_out_warning::GetDistractionInfo(internal_analysis_distraction_info& info) {
    return Distrac->GetDistractionInfo(info);
}

}  // namespace tongxing