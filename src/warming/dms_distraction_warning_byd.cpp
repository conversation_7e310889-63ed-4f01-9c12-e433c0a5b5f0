#include "dms_distraction_warning_byd.h"
#include <math.h>
#include <sys/stat.h>
#include <sys/types.h>
#include <time.h>
#include <unistd.h>
#include <algorithm>
#include <fstream>
#include "CalmCarLog.h"
#include "cc_resource_register.h"
#include "json.h"
#include "svm_model.h"

int WINDOW_TIME;                //分神时间窗
float DISTRACTION_THR;          //分神阈值
int HEAD_YAW_BIAS_WINDOW_TIME;  //head_yaw_bias时间窗

float HEADPOSE_PITCH_THR;  //头部姿态pitch 阈值
float GAZE_PITCH_THR;      //视线 pitch阈值

float HEADPOSE_YAW_THR;  //头部姿态yaw 阈值
float GAZE_YAW_THR;      //视线 yaw阈值

float PITCH_UP;    //临时过滤低头误报闭眼pitch
float PITCH_DOWN;  //临时过滤低头误报闭眼pitch
float YAW_LEFT;    //临时过滤偏头误报闭眼yaw偏差值
float YAW_RIGHT;   //临时过滤偏头误报闭眼yaw偏差值
float ROLL_LIFT;   //临时过滤偏头误报分神roll
float ROLL_RIGHT;  //临时过滤偏头误报分神roll

float STEERING_WHEEL_ANGLE_MIN;  //标定方向盘转角最小值
float STEERING_WHEEL_ANGLE_MAX;  //标定方向盘转角最大值

float CALIBRATE_HEADPOSE_YAW_NORMAL_MIN;    //标定人脸角度yaw最小值
float CALIBRATE_HEADPOSE_YAW_NORMAL_MAX;    //标定人脸角度yaw最大值
float CALIBRATE_HEADPOSE_PITCH_NORMAL_MIN;  //标定人脸角度pitch最小值
float CALIBRATE_HEADPOSE_PITCH_NORMAL_MAX;  //标定人脸角度pitch最大值
float CALIBRATE_HEADPOSE_ROLL_NORMAL_MIN;   //标定人脸角度roll最小值
float CALIBRATE_HEADPOSE_ROLL_NORMAL_MAX;   //标定人脸角度roll最大值

float HEADPOSE_YAW_NORMAL_MIN;    //正常人脸角度yaw最小值
float HEADPOSE_YAW_NORMAL_MAX;    //正常人脸角度yaw最大值
float HEADPOSE_PITCH_NORMAL_MIN;  //正常人脸角度pitch最小值
float HEADPOSE_PITCH_NORMAL_MAX;  //正常人脸角度pitch最大值
float HEADPOSE_ROLL_NORMAL_MIN;   //正常人脸角度roll最小值
float HEADPOSE_ROLL_NORMAL_MAX;   //正常人脸角度roll最大值

float HEAD_POSE_YAW_L;    //head pose yaw角 左方向绝对偏差值
float HEAD_POSE_YAW_R;    //head pose yaw角 右方向绝对偏差值
float HEAD_POSE_PITCH_U;  //head pose pitch 上方向绝对偏差值
float HEAD_POSE_PITCH_D;  //head pose pitch 下方向绝对偏差值

float HEAD_POSE_SPE_GLASSES_YAW_L;  //戴眼镜且眼睛不可见时设置的更多偏差
float HEAD_POSE_SPE_GLASSES_YAW_R;
float HEAD_POSE_SPE_GLASSES_PITCH_U;
float HEAD_POSE_SPE_GLASSES_PITCH_D;

float HEAD_YAW_L_OFFSET;  //视线融合头部姿态偏差值(用于判断是否优先使用视线融合做分心判断)
float HEAD_YAW_R_OFFSET;  //视线融合头部姿态偏差值(用于判断是否优先使用视线融合做分心判断)

float RIGHTEYE_UP_DOWN_PROPORTION;  //视线往下看,右眼绝对值
float LEFTEYE_UP_DOWN_PROPORTION;   //视线往下看,左眼绝对值

float HEAD_YAW_BIAS;  //头部yaw最大最小值的绝对值，用于辅助判断是否注释一个点位，延迟报警

int FUSION_USE_EYE;                                  //融合角度计算所使用的眼睛
int REGION_MAPPING_WIDTH;                            //区域映射宽度
int REGION_MAPPING_HEIGHT;                           //区域映射高度
float TOLERATE_PERCENTAGE;                           //容忍度百分比
std::vector<std::vector<cv::Point2f>> REGION_HULLS;  //区域轮廓点集

namespace tongxing {
DistractionWarn::DistractionWarn() {
    distraction_short_result.clear();
    distraction_long_result.clear();
    distraction_3s_result.clear();
    head_yaw_3s_vec.clear();
    glasses_vec.clear();
    mask_vec.clear();
    temp_vec.clear();
    facevalid_mix_distra.clear();

    //解析分神内置json
    Json::Reader config_json_reader;
    Json::Value config_root;
    auto config_json_data = CcResourcDataRegister::instance().get_function("sight.json");

    std::string config_doc = std::string((char*)config_json_data.second, config_json_data.first);
    if (!config_json_reader.parse(config_doc, config_root)) {
        TX_LOG_FATAL("TX DMS", "DistractionWarn Parse json config file failed!");
        // return -1;
    }
    //分神参数解析如下
    HEADPOSE_PITCH_THR = config_root["headpose_pitch_threshold"].asDouble();
    GAZE_PITCH_THR = config_root["gaze_pitch_threshold"].asDouble();
    HEADPOSE_YAW_THR = config_root["headpose_yaw_threshold"].asDouble();
    GAZE_YAW_THR = config_root["gaze_yaw_threshold"].asDouble();

    WINDOW_TIME = config_root["window_time"].asInt();
    DISTRACTION_THR = config_root["distraction_thr"].asDouble();
    HEAD_YAW_BIAS_WINDOW_TIME = config_root["head_yaw_bias_window_time"].asInt();

    CALIBRATE_HEADPOSE_YAW_NORMAL_MIN = config_root["calibrate_headpose_yaw_min"].asDouble();
    CALIBRATE_HEADPOSE_YAW_NORMAL_MAX = config_root["calibrate_headpose_yaw_max"].asDouble();
    CALIBRATE_HEADPOSE_PITCH_NORMAL_MIN = config_root["calibrate_headpose_pitch_min"].asDouble();
    CALIBRATE_HEADPOSE_PITCH_NORMAL_MAX = config_root["calibrate_headpose_pitch_max"].asDouble();
    CALIBRATE_HEADPOSE_ROLL_NORMAL_MIN = config_root["calibrate_headpose_roll_min"].asDouble();
    CALIBRATE_HEADPOSE_ROLL_NORMAL_MAX = config_root["calibrate_headpose_roll_max"].asDouble();

    PITCH_DOWN = config_root["pitch_down"].asDouble();
    PITCH_UP = config_root["pitch_up"].asDouble();
    YAW_LEFT = config_root["yaw_left"].asDouble();
    YAW_RIGHT = config_root["yaw_right"].asDouble();
    ROLL_LIFT = config_root["roll_left"].asDouble();
    ROLL_RIGHT = config_root["roll_right"].asDouble();

    STEERING_WHEEL_ANGLE_MIN = config_root["steering_wheel_angle_min"].asDouble();
    STEERING_WHEEL_ANGLE_MAX = config_root["steering_wheel_angle_max"].asDouble();

    HEADPOSE_YAW_NORMAL_MIN = config_root["headpose_yaw_normal_min"].asDouble();
    HEADPOSE_YAW_NORMAL_MAX = config_root["headpose_yaw_normal_max"].asDouble();
    HEADPOSE_PITCH_NORMAL_MIN = config_root["headpose_pitch_normal_min"].asDouble();
    HEADPOSE_PITCH_NORMAL_MAX = config_root["headpose_pitch_normal_max"].asDouble();
    HEADPOSE_ROLL_NORMAL_MIN = config_root["headpose_roll_normal_min"].asDouble();
    HEADPOSE_ROLL_NORMAL_MAX = config_root["headpose_roll_normal_max"].asDouble();

    HEAD_POSE_YAW_L = config_root["head_pose_yaw_left_offset"].asDouble();
    HEAD_POSE_YAW_R = config_root["head_pose_yaw_right_offset"].asDouble();
    HEAD_POSE_PITCH_U = config_root["head_pose_pitch_up_offset"].asDouble();
    HEAD_POSE_PITCH_D = config_root["head_pose_pitch_down_offset"].asDouble();
#if defined(BYD_HA6) || defined(BYD_EQ) || defined(BYD_EQ_R)
    HEAD_POSE_SPE_GLASSES_YAW_L = config_root["head_pose_spe_glasses_yaw_left_offset"].asDouble();
    HEAD_POSE_SPE_GLASSES_YAW_R = config_root["head_pose_spe_glasses_yaw_right_offset"].asDouble();
    HEAD_POSE_SPE_GLASSES_PITCH_U = config_root["head_pose_spe_glasses_pitch_up_offset"].asDouble();
    HEAD_POSE_SPE_GLASSES_PITCH_D =
        config_root["head_pose_spe_glasses_pitch_down_offset"].asDouble();
#else
    HEAD_POSE_SPE_GLASSES_YAW_L = 0.0f;
    HEAD_POSE_SPE_GLASSES_YAW_R = 0.0f;
    HEAD_POSE_SPE_GLASSES_PITCH_U = 0.0f;
    HEAD_POSE_SPE_GLASSES_PITCH_D = 0.0f;
#endif

    HEAD_YAW_L_OFFSET = config_root["headgaze_yaw_l_offset"].asDouble();
    HEAD_YAW_R_OFFSET = config_root["headgaze_yaw_r_offset"].asDouble();

    RIGHTEYE_UP_DOWN_PROPORTION = config_root["righteye_up_down_proportion"].asDouble();
    LEFTEYE_UP_DOWN_PROPORTION = config_root["lefteye_up_down_proportion"].asDouble();

    HEAD_YAW_BIAS = config_root["head_yaw_bias"].asDouble();

    if (config_root.isMember("fusion_use_eye")) {
        FUSION_USE_EYE = config_root["fusion_use_eye"].asInt();
    }
    if (config_root.isMember("region_mapping_width")) {
        REGION_MAPPING_WIDTH = config_root["region_mapping_width"].asInt();
    }
    if (config_root.isMember("region_mapping_height")) {
        REGION_MAPPING_HEIGHT = config_root["region_mapping_height"].asInt();
    }
    if (config_root.isMember("tolerate_percentage")) {
        TOLERATE_PERCENTAGE = config_root["tolerate_percentage"].asFloat();
    }
    if (config_root.isMember("region_hull")) {
        cc_assert(config_root["region_hull"].isArray());
        std::vector<cv::Point2f> hull;
        REGION_HULLS.clear();
        for (int k = 0; k < config_root["region_hull"].size(); k++) {
            hull.clear();
            for (int i = 0; i < config_root["region_hull"][k].size(); i++) {
                hull.clear();
                for (int j = 0; j < config_root["region_hull"][k][i].size(); j += 2) {
                    cc_assert(config_root["region_hull"][k][i].size() % 2 == 0);

                    cv::Point2f point;
                    point.x = config_root["region_hull"][k][i][j].asFloat();
                    point.y = config_root["region_hull"][k][i][j + 1].asFloat();
                    hull.push_back(point);
                }
                // std::cout << "hull.size():" << hull.size() << std::endl;
                REGION_HULLS.push_back(hull);
            }
            std::cout << "use inside config json,REGION_HULLS.size():" << REGION_HULLS.size()
                      << std::endl;
        }
    }

    //标定参数如下
    int queue_length = 500;
    int k_num = 1;               //test
    float cluster_radius = 5.0;//3.0;  //5.0;
    float threshold_100f = 0.8;
    float threshold_200f = 0.5;
    float threshold_longerf = 0.35;  //0.55;

    float eye_cluster_radius = 1.6;  //2.0;
    float eye_threshold_100f = 0.7;
    float eye_threshold_200f = 0.55;     //0.6;
    float eye_threshold_longerf = 0.45;  //0.5;

    calibrator.reset(new HeadPoseCalibrator(
        queue_length, 100, k_num, cluster_radius, threshold_100f, threshold_200f, threshold_longerf,
        eye_cluster_radius, eye_threshold_100f, eye_threshold_200f, eye_threshold_longerf));
    calibrator->init();

    QuickFillingCaliData();
}

DistractionWarn::~DistractionWarn() {}

void DistractionWarn::Update(const Distraction_Info& info) {
    std::pair<long, bool> temp_pair;
    temp_pair.first = info.time_input;
    temp_pair.second = info.distraction_status;

    distraction_long_result.emplace_back(temp_pair);
    if (auto_calibration) {
        distraction_short_result.emplace_back(temp_pair);
        if (distraction_short_result.size() >= 2) {
            if ((distraction_short_result.back().first - distraction_short_result.front().first) >
                31000) {
                distraction_short_result.pop_front();  //移除队列最前面的数据
            }
        }
    }
    if (distraction_long_result.size() >= 2) {
        if ((distraction_long_result.back().first - distraction_long_result.front().first) > 6000) {
            distraction_long_result.pop_front();  //移除队列最前面的数据
        }
    }

    distraction_3s_result.clear();
    GetDataQueue(distraction_long_result, distraction_3s_result, window_time);  //获取3.5s的数据

    cache_time = info.time_input;

    return;
}

void DistractionWarn::GetDataQueue(std::deque<std::pair<long, bool>>& input_distraction_deque,
                                   std::deque<std::pair<long, bool>>& out_distraction_deque,
                                   long time_gap) {
    if (input_distraction_deque.size() == 0) {
        return;
    }
    auto last_ts = input_distraction_deque.back();
    for (const auto& v : input_distraction_deque) {
        if (last_ts.first - v.first > time_gap)
            continue;
        out_distraction_deque.emplace_back(v);
    }
    return;
}

//这里计算各个报警状态

//计算是否持续分神
bool DistractionWarn::GetResult(std::deque<std::pair<long, bool>>& distraction_deque,
                                long time_gap) {
    // std::cout << "diff:" << distraction_deque.back().first- distraction_deque.front().first << " " << distraction_deque.back().first <<
    // " " << distraction_deque.front().first << " time_gap:" << time_gap << std::endl;
    if ((distraction_deque.back().first - distraction_deque.front().first) < time_gap)
        return false;

    int count = 0;
    for (const auto& v : distraction_deque) {
        if (v.second == true)
            count++;
    }
    float thf = count * 1.0f / distraction_deque.size();
    bool bRet = (thf >= distraction_thr) && (distraction_deque.back().second) && (distraction_deque.front().second);
    // std::cout << "thf:" << thf << " distraction_thr:" << distraction_thr << " count:" << count << " distraction_deque.size():" << distraction_deque.size() << std::endl;
    return bRet;
}

bool DistractionWarn::GetMixDistraStatus(bool isFaceValid,
                                         const float face_keypoints_score,
                                         const TXCarInfo* car_info,
                                         bool camera_occlusion,
                                         bool distraction_status,
                                         long now_ts) {
    const size_t min_distraction_frames_to_trigger_no_face =
        2;  // 进行无人脸帧统计前面需要的分心帧数

    bool mixdistra_status = false, is_trigger = false;  //后2帧不是人脸不分心则触发
    float mix_distra_ratio = 0.0;
    int count = 0;

    int mixstatus =
        (!isFaceValid) ? 0 : (!distraction_status ? 1 : 2);  //0:无人脸;1:人脸不分心;2:人脸分心

    if (car_info->speed < 20)
        mixstatus = 1;  //速度小于20km/h，不分心

    // 记录无人脸帧前分心帧的具体分心原因
    if (mixstatus == 2 && headpose_reason.find("headyaw maxlimit") == std::string::npos &&
        headpose_reason.find("headyaw minlimit") == std::string::npos) {
        mixstatus = 0;  // 分心不是因为头部yaw触发的，则不视为分心的统计
    }
    if (mixstatus == 1 && face_keypoints_score < kFaceAngleThreshold && face_keypoints_score > 0) {
        mixstatus =
            2;  // 将判断分心主流程中的人脸关键点置信度比较低的情况，移到混合统计分心+无人脸的函数中
    }

    std::pair<long, int> tmp_pair = {now_ts, mixstatus};
    facevalid_mix_distra.emplace_back(tmp_pair);

    if (facevalid_mix_distra.size() >= 2) {
        size_t data_size = facevalid_mix_distra.size();
        if ((facevalid_mix_distra[data_size - 1].second == 1) &&
            (facevalid_mix_distra[data_size - 2].second == 1)) {
            is_trigger = false;
            count = 0;
            facevalid_mix_distra.clear();
        }
    }

    if (car_info->speed >= 20 && !camera_occlusion && facevalid_mix_distra.size() >= 3) {
        if (facevalid_mix_distra.back().first - facevalid_mix_distra.front().first >= window_time) {
            size_t data_size = facevalid_mix_distra.size();
            for (size_t i = 0; i < data_size; i++) {
                int status = facevalid_mix_distra[i].second;
                if (!is_trigger) {
                    if (status == 2) {
                        count++;
                    } else {
                        count = 0;
                    }
                    if (count >= min_distraction_frames_to_trigger_no_face)
                        is_trigger = true;
                } else {
                    if (status != 1)
                        count++;
                }
            }
            facevalid_mix_distra.pop_front();
        }

        if (facevalid_mix_distra.size() > 0)
            mix_distra_ratio = (float)count / (float)facevalid_mix_distra.size();
    }

    if (mix_distra_ratio >= 0.7 && is_trigger) {
        mixdistra_status = true;
    }
    // std::cout << "mixdistra_status:" << mixdistra_status << " is_trigger:" << is_trigger << " mix_distra_ratio:" << mix_distra_ratio << std::endl;
    // std::cout << "count:" << count << " facevalid_mix_distra.size():" << facevalid_mix_distra.size()  << " mixstatus:" << mixstatus << std::endl;
    return mixdistra_status;
}

//计算短时分心，累计分神
bool DistractionWarn::GetShortTimeResult(std::deque<std::pair<long, bool>>& distraction_deque,
                                         long interval_times) {
    if (distraction_deque.size() == 0)
        return false;
    // if ((distraction_deque.back().first - distraction_deque.front().first) < 30000)
    //     return false;

    long start_time = -1;  // -1 表示未开始计算
    long total_true_time = 0;
    for (size_t i = 0; i < distraction_deque.size(); ++i) {
        long timestamp = distraction_deque[i].first;
        bool is_true = distraction_deque[i].second;

        if (is_true) {
            if (start_time == -1) {
                start_time = timestamp;  // 记录一个新的连续 true 段落的起始时间
            }
        } else {
            if (start_time != -1) {
                // 遇到 false，结束当前连续 true 段落的计算
                total_true_time += distraction_deque[i - 1].first - start_time;
                start_time = -1;  // 重置为未开始
            }
        }

        if (total_true_time >= interval_times) {
            return true;
        }
    }

    // 如果最后一个段落是 true，且没有遇到 false
    if (start_time != -1) {
        total_true_time += distraction_deque.back().first - start_time;
    }

    if (total_true_time >= interval_times) {
        return true;
    }

    std::deque<std::pair<long, bool>> temp_deque2;
    temp_deque2.clear();
    long cost_time2 = 0;
    for (const auto& v : distraction_deque) {
        if (v.second == true) {
            temp_deque2.clear();
            cost_time2 = 0;
        } else {
            //正常驾驶区域持续时长≥2s,清空短时分心数据
            temp_deque2.emplace_back(v);
            if (temp_deque2.size() >= 2) {
                cost_time2 = temp_deque2.back().first - temp_deque2.front().first;
            }
            if (cost_time2 >= 2000) {
                distraction_deque.clear();
                return false;
            }
        }
    }
    return false;
}

//降级根据连续帧数计算
bool DistractionWarn::GetDegradeResult2(std::deque<std::pair<long, bool>>& distraction_deque,
                                        int cnt) {
    int count = 0;
    for (const auto& v : distraction_deque) {
        if (v.second == false) {
            // return false;
            count++;

        } else {
            count = 0;
        }

        if (count >= cnt)
            return true;
    }
    return false;
}

long DistractionWarn::GetContinueDistractionTime(
    const std::deque<std::pair<long, bool>> distraction_deque) {
    if (distraction_deque.size() == 0)
        return 0;

    long current_consecutive_time = 0;
    int cnt = 0;

    for (size_t i = 1; i < distraction_deque.size(); ++i) {
        long time_diff = distraction_deque[i].first - distraction_deque[i - 1].first;

        if (distraction_deque[i].second == true) {
            if (distraction_deque[i - 1].second == true) {
                current_consecutive_time += time_diff;
                // cnt = 0;  // Reset count as the streak continues.
            } else {
                // Previous frame was false, but current is true, reset the counter.
                cnt = 0;
                current_consecutive_time += time_diff;
            }
        } else {
            // Current frame is false, increment the non-continuous count.
            cnt++;
            // Only reset `current_consecutive_time` if the count of non-continuous frames exceeds the threshold.
            if (cnt >= 1) {
                current_consecutive_time = 0;
            }
        }

        // distraction_info.distraction_continue_time = current_consecutive_time;
    }
    return current_consecutive_time;
}

float DistractionWarn::GetContinueDistractionPercent(
    const std::deque<std::pair<long, bool>> distraction_deque, long time_gap) {
    if (distraction_deque.size() == 0 ||
        (distraction_deque.back().first - distraction_deque.front().first) < time_gap)
        return 0.0f;

    int count = 0;
    for (const auto& v : distraction_deque) {
        if (v.second == true)
            count++;
    }
    // return true;
    float thf = count * 1.0f / distraction_deque.size();

    return thf;
}

long DistractionWarn::GetSumDistractionTime(
    const std::deque<std::pair<long, bool>> distraction_deque) {
    long distraction_sum_time = 0;

    if (distraction_deque.size() == 0)
        return 0;

    long start_time = -1;  // -1 表示未开始计算
    for (size_t i = 0; i < distraction_deque.size(); ++i) {
        long timestamp = distraction_deque[i].first;
        bool is_true = distraction_deque[i].second;

        if (is_true) {
            if (start_time == -1) {
                start_time = timestamp;  // 记录一个新的连续 true 段落的起始时间
            }
        } else {
            if (start_time != -1) {
                // 遇到 false，结束当前连续 true 段落的计算
                distraction_sum_time += distraction_deque[i - 1].first - start_time;
                start_time = -1;  // 重置为未开始
            }
        }
    }

    // 如果最后一个段落是 true，且没有遇到 false
    if (start_time != -1) {
        distraction_sum_time += distraction_deque.back().first - start_time;
    }

    return distraction_sum_time;
}

long DistractionWarn::GetContinueFrontTime(
    const std::deque<std::pair<long, bool>> distraction_deque) {
    if (distraction_deque.size() == 0)
        return 0;

    long distraction_front_continue_time = 0;
    // std::vector<long> time_vector;  //用于记录时间差数组

    std::deque<std::pair<long, bool>> temp_deque2;
    temp_deque2.clear();
    long cost_time2 = 0;
    for (const auto& v : distraction_deque) {
        if (v.second == false) {
            temp_deque2.emplace_back(v);
            if (temp_deque2.size() >= 2) {
                cost_time2 = temp_deque2.back().first - temp_deque2.front().first;
            }
            distraction_front_continue_time = cost_time2;
        } else {
            temp_deque2.clear();
            cost_time2 = 0;
        }
        // time_vector.emplace_back(cost_time2);
    }
    // distraction_front_continue_time = *(std::max_element(
    //     time_vector.begin(), time_vector.end()));  //找出时间窗中最大时间作为结果输出

    return distraction_front_continue_time;
}

void DistractionWarn::Reset() {
    ok_flag = false;

    distraction_short_result.clear();
    distraction_long_result.clear();

    // head_yaw_3s_vec.clear();
    // glasses_vec.clear();
    // mask_vec.clear();
    // temp_vec.clear();
    head_yaw_3s_min = -99.0f;
    head_yaw_3s_max = 99.0f;

    alarm_start_time = cache_time;

    histor_warn_type = DISTRACTION_NORMAL;
    return;
}

void DistractionWarn::ResetNofaceMixdata() {
    facevalid_mix_distra.clear();
}

DistractionType DistractionWarn::GetWarnStatus(int speed) {
    DistractionType status = DISTRACTION_NORMAL;
    //可视化
    distraction_info = {0};
    distraction_info.distraction_continue_time = GetContinueDistractionTime(distraction_3s_result);
    distraction_info.distraction_continue_percent =
        GetContinueDistractionPercent(distraction_3s_result, window_time - 150);
    distraction_info.distraction_front_continue_time = GetContinueFrontTime(distraction_3s_result);
    distraction_info.time_gap = 0;
    if (distraction_3s_result.size() > 0)
        distraction_info.time_gap =
            (distraction_3s_result.back().first - distraction_3s_result.front().first);
    //可视化end

    //提前报分神优化，延迟报警(只对长时分心有效)
    bool is_head_yawbias = false;
    if (head_yaw_3s_min > -90.0f && head_yaw_3s_max < 90.f) {
        if (fabs(head_yaw_3s_min - head_yaw_3s_max) >= head_yaw_bias)
            is_head_yawbias = true;
    }
    if (head_yaw_3s_vec.size() > 2 &&
        (head_yaw_3s_vec.back().first - head_yaw_3s_vec.front().first) >=
            (head_yaw_bias_window_time - 150) &&
        is_head_yawbias && histor_warn_type == DISTRACTION_NORMAL) {
        distraction_reason = distraction_reason + " no_distraction2";
    } else {
        bool speed_50_3s = GetResult(distraction_3s_result, window_time - 150);
        // printf("speed_50_3s:%d\n", speed_50_3s);
        if (speed_50_3s && speed >= 20) {
            status = DISTRACTION_FATIGUE1;
        }
    }

#if 0  //defined(BYD_EQ) //和短时分心判断相关需保留
    std::deque<std::pair<long, bool>> distraction_temp_result;
    distraction_temp_result.clear();
    GetDataQueue(distraction_short_result, distraction_temp_result, 30100);  //获取30s的数据

    distraction_info.distraction_sum_time = GetSumDistractionTime(distraction_temp_result);

    bool short_time_distraction = GetShortTimeResult(distraction_temp_result, 10000);
    if (short_time_distraction && speed >= 20) {
        status = DISTRACTION_FATIGUE2;
    }
#endif

    if (histor_warn_type < status) {
        histor_warn_type = status;      //最新的报警等级，存在历史结果
        alarm_start_time = cache_time;  //升级后,更新报警开始时间
        alarm_ok_start_time = cache_time;
    } else {
        status = histor_warn_type;  //持续报上次结果
        alarm_ok_end_time = cache_time;
    }

    //判断降级（注视正常驾驶区域，持续时长≥1s，则退出分心状态）视为触发降级，即为正常状态
    alarm_end_time = cache_time;

    //只触发降级
    std::deque<std::pair<long, bool>> temp_result;
    GetDataQueue(distraction_3s_result, temp_result, 1000);
    if (GetDegradeResult2(temp_result, 3)) {
        // std::cout << "Degrade distract..." << std::endl;
        Reset();
        status = DISTRACTION_NORMAL;
    }

    if (ok_flag) {  //收到ok,清除报警
        // std::cout << "ok_flag, clear distract..." << std::endl;
        Reset();
        status = DISTRACTION_NORMAL;
    }
    return status;
}

void DistractionWarn::SetOk() {
    ok_flag = true;
}

//自动标定分神
TXDistracCaliStatus DistractionWarn::auto_calibration_distraction(TXDmsFaceInfo face_info,
                                                                  const TXCarInfo* car_info,
                                                                  const float leye_uper_curve_score,
                                                                  const float reye_uper_curve_score,
                                                                  long now_ts) {
    // 读取json文件，本地调试模式
    std::string config_file = "sight.json";
    //配置文件存在，则从配置文件中读取
    if (!read_json && access(config_file.c_str(), F_OK) == 0) {
        Json::Reader json_reader2;
        Json::Value root2;
        std::ifstream infile2(config_file, std::ios::binary);
        if (!infile2.is_open()) {
            std::cout << "Open  sight config file failed!" << std::endl;
        }

        if (!json_reader2.parse(infile2, root2)) {
            std::cout << "Parse  sight json config file failed!" << std::endl;
        } else {
            read_json = true;
            log_switch = root2["log_switch"].asBool();

            headpose_pitch_threshold = root2["headpose_pitch_threshold"].asDouble();
            gaze_pitch_threshold = root2["gaze_pitch_threshold"].asDouble();
            headpose_yaw_threshold = root2["headpose_yaw_threshold"].asDouble();
            gaze_yaw_threshold = root2["gaze_yaw_threshold"].asDouble();

            window_time = root2["window_time"].asInt();
            distraction_thr = root2["distraction_thr"].asDouble();

            head_yaw_bias_window_time = root2["head_yaw_bias_window_time"].asInt();

            calibrate_headpose_yaw_min = root2["calibrate_headpose_yaw_min"].asDouble();
            calibrate_headpose_yaw_max = root2["calibrate_headpose_yaw_max"].asDouble();
            calibrate_headpose_pitch_min = root2["calibrate_headpose_pitch_min"].asDouble();
            calibrate_headpose_pitch_max = root2["calibrate_headpose_pitch_max"].asDouble();
            calibrate_headpose_roll_min = root2["calibrate_headpose_roll_min"].asDouble();
            calibrate_headpose_roll_max = root2["calibrate_headpose_roll_max"].asDouble();

            pitch_down = root2["pitch_down"].asDouble();
            pitch_up = root2["pitch_up"].asDouble();
            yaw_left = root2["yaw_left"].asDouble();
            yaw_right = root2["yaw_right"].asDouble();
            roll_left = root2["roll_left"].asDouble();
            roll_right = root2["roll_right"].asDouble();

            steering_wheel_angle_min = root2["steering_wheel_angle_min"].asDouble();
            steering_wheel_angle_max = root2["steering_wheel_angle_max"].asDouble();
            headpose_yaw_normal_min = root2["headpose_yaw_normal_min"].asDouble();
            headpose_yaw_normal_max = root2["headpose_yaw_normal_max"].asDouble();
            headpose_pitch_normal_min = root2["headpose_pitch_normal_min"].asDouble();
            headpose_pitch_normal_max = root2["headpose_pitch_normal_max"].asDouble();
            headpose_roll_normal_min = root2["headpose_roll_normal_min"].asDouble();
            headpose_roll_normal_max = root2["headpose_roll_normal_max"].asDouble();
            headpose_yaw_left = root2["head_pose_yaw_left_offset"].asDouble();
            headpose_yaw_right = root2["head_pose_yaw_right_offset"].asDouble();
            headpose_pitch_up = root2["head_pose_pitch_up_offset"].asDouble();
            headpose_pitch_down = root2["head_pose_pitch_down_offset"].asDouble();
#if defined(BYD_HA6) || defined(BYD_EQ) || defined(BYD_EQ_R)
            headpose_spe_glasses_yaw_left =
                root2["head_pose_spe_glasses_yaw_left_offset"].asDouble();
            headpose_spe_glasses_yaw_right =
                root2["head_pose_spe_glasses_yaw_right_offset"].asDouble();
            headpose_spe_glasses_pitch_up =
                root2["head_pose_spe_glasses_pitch_up_offset"].asDouble();
            headpose_spe_glasses_pitch_down =
                root2["head_pose_spe_glasses_pitch_down_offset"].asDouble();
#else
            headpose_spe_glasses_yaw_left = 0.0f;
            headpose_spe_glasses_yaw_right = 0.0f;
            headpose_spe_glasses_pitch_up = 0.0f;
            headpose_spe_glasses_pitch_down = 0.0f;
#endif
            headgaze_yaw_l_offset = root2["headgaze_yaw_l_offset"].asDouble();
            headgaze_yaw_r_offset = root2["headgaze_yaw_r_offset"].asDouble();

            righteye_up_down_proportion = root2["righteye_up_down_proportion"].asDouble();
            lefteye_up_down_proportion = root2["lefteye_up_down_proportion"].asDouble();

            head_yaw_bias = root2["head_yaw_bias"].asDouble();

            if (root2.isMember("fusion_use_eye")) {
                fusion_use_eye = root2["fusion_use_eye"].asInt();
            }

            if (root2.isMember("region_mapping_width")) {
                region_mapping_width = root2["region_mapping_width"].asInt();
            }
            if (root2.isMember("region_mapping_height")) {
                region_mapping_height = root2["region_mapping_height"].asInt();
            }
            if (root2.isMember("tolerate_percentage")) {
                tolerate_percentage = root2["tolerate_percentage"].asFloat();
            }
            if (root2.isMember("region_hull")) {
                cc_assert(root2["region_hull"].isArray());

                std::vector<cv::Point2f> hull;
                for (int k = 0; k < root2["region_hull"].size(); k++) {
                    hull.clear();
                    for (int i = 0; i < root2["region_hull"][k].size(); i++) {
                        hull.clear();
                        for (int j = 0; j < root2["region_hull"][k][i].size(); j += 2) {
                            cc_assert(root2["region_hull"][k][i].size() % 2 == 0);

                            cv::Point2f point;
                            point.x = root2["region_hull"][k][i][j].asFloat();
                            point.y = root2["region_hull"][k][i][j + 1].asFloat();
                            hull.push_back(point);
                        }
                        std::cout << "hull.size():" << hull.size() << std::endl;
                        region_hulls.push_back(hull);
                    }
                    std::cout << "use outside config json,region_hulls.size():"
                              << region_hulls.size() << std::endl;
                }
            }
        }
    }
    // //test
    // log_switch = true;
    if (log_switch) {
        printf("read_json:%d,headpose_yaw_left:%f,headpose_yaw_right:%f,headpose_pitch_up:%f,"
               "headpose_pitch_down:%f \n",
               read_json, headpose_yaw_left, headpose_yaw_right, headpose_pitch_up,
               headpose_pitch_down);
    }

    // 增加标定模块相关打印
    if (now_ts - last_ts >= 1000) {
        std::cout << "[DMS CALIBRATION]:" << auto_calibration << " [" << headpose_yaw_ << ","
                  << headpose_pitch_ << "," << headpose_roll_ << "] [" << gaze_left_eye_yaw_ << ","
                  << gaze_left_eye_pitch_ << "] [" << gaze_right_eye_yaw_ << ","
                  << gaze_right_eye_pitch_ << "]"
                  << " [" << leye_uper_curve_score_mean_ << "," << reye_uper_curve_score_mean_
                  << "]" << std::endl;
        std::cout << car_info->speed << " [" << face_info.head_yaw << ", " << face_info.head_pitch
                  << "," << face_info.head_roll << "] [" << face_info.left_eye_landmark.yaw << ","
                  << face_info.left_eye_landmark.pitch << "] [" << face_info.right_eye_landmark.yaw
                  << "," << face_info.right_eye_landmark.pitch << "] [" << leye_uper_curve_score
                  << "," << reye_uper_curve_score << "]" << std::endl;
        last_ts = now_ts;
    }

    //自动标定分神逻辑
    if (car_info->speed >= 20 && car_info->gear == TXGearPition::FORWARD) {
        bool is_mask = (face_info.isMask == 1) ?  true : false;
        if (!auto_calibration) {
            //判断人脸角度是否合理，不合理则不进行标定
            if (read_json) {
                if (calibrate_headpose_yaw_min > face_info.head_yaw ||
                    calibrate_headpose_yaw_max < face_info.head_yaw ||
                    calibrate_headpose_pitch_min > face_info.head_pitch ||
                    calibrate_headpose_pitch_max < face_info.head_pitch ||
                    calibrate_headpose_roll_min > face_info.head_roll ||
                    calibrate_headpose_roll_max < face_info.head_roll) {
                    if (log_switch) {
                        printf("DistractionWarn  "
                               "不满足人脸角度正常范围，不可进行自动标定...........\n");
                    }

                    return CALIBRATE_CONDITION_UNDONE;
                }

                if (car_info->steer_whl_snsr_rad < steering_wheel_angle_min ||
                    car_info->steer_whl_snsr_rad > steering_wheel_angle_max) {
                    if (log_switch) {
                        printf("DistractionWarn  "
                               "不满足方向盘转角正常范围，不可进行自动标定...........\n");
                    }

                    return CALIBRATE_CONDITION_UNDONE;
                }
            } else {
                headpose_pitch_threshold = HEADPOSE_PITCH_THR;
                gaze_pitch_threshold = GAZE_PITCH_THR;
                headpose_yaw_threshold = HEADPOSE_YAW_THR;
                gaze_yaw_threshold = GAZE_YAW_THR;

                headgaze_yaw_l_offset = HEAD_YAW_L_OFFSET;
                headgaze_yaw_r_offset = HEAD_YAW_R_OFFSET;

                righteye_up_down_proportion = RIGHTEYE_UP_DOWN_PROPORTION;
                lefteye_up_down_proportion = LEFTEYE_UP_DOWN_PROPORTION;

                head_yaw_bias = HEAD_YAW_BIAS;
                head_yaw_bias_window_time = HEAD_YAW_BIAS_WINDOW_TIME;

                window_time = WINDOW_TIME;
                distraction_thr = DISTRACTION_THR;

                fusion_use_eye = FUSION_USE_EYE;
                region_mapping_width = REGION_MAPPING_WIDTH;
                region_mapping_height = REGION_MAPPING_HEIGHT;
                tolerate_percentage = TOLERATE_PERCENTAGE;
                region_hulls = REGION_HULLS;

                if (CALIBRATE_HEADPOSE_YAW_NORMAL_MIN - ((is_mask) ? HEAD_POSE_SPE_GLASSES_YAW_L : 0) > face_info.head_yaw ||
                    CALIBRATE_HEADPOSE_YAW_NORMAL_MAX + ((is_mask) ? HEAD_POSE_SPE_GLASSES_YAW_R : 0) < face_info.head_yaw ||
                    CALIBRATE_HEADPOSE_PITCH_NORMAL_MIN - ((is_mask) ? HEAD_POSE_SPE_GLASSES_PITCH_D : 0) > face_info.head_pitch ||
                    CALIBRATE_HEADPOSE_PITCH_NORMAL_MAX + ((is_mask) ? HEAD_POSE_SPE_GLASSES_PITCH_U : 0) < face_info.head_pitch ||
                    CALIBRATE_HEADPOSE_ROLL_NORMAL_MIN > face_info.head_roll ||
                    CALIBRATE_HEADPOSE_ROLL_NORMAL_MAX < face_info.head_roll) {
                    if (log_switch) {
                        printf("DistractionWarn "
                               "不满足人脸角度正常范围，不可进行自动标定2...........\n");
                    }
                    return CALIBRATE_CONDITION_UNDONE;
                }
                if (car_info->steer_whl_snsr_rad < STEERING_WHEEL_ANGLE_MIN ||
                    car_info->steer_whl_snsr_rad > STEERING_WHEEL_ANGLE_MAX) {
                    if (log_switch) {
                        printf("DistractionWarn  "
                               "不满足方向盘转角正常范围，不可进行自动标定2...........\n");
                    }

                    return CALIBRATE_CONDITION_UNDONE;
                }
            }
        }
        StartCalibration(is_mask, face_info.head_yaw, face_info.head_pitch, face_info.head_roll,
                         face_info.left_eye_landmark.yaw, face_info.left_eye_landmark.pitch,
                         face_info.right_eye_landmark.yaw, face_info.right_eye_landmark.pitch,
                         face_info.left_eye_landmark.pupil_score,
                         face_info.right_eye_landmark.pupil_score, leye_uper_curve_score,
                         reye_uper_curve_score);

        headpose_yaw_ = temp_headpose_yaw_mean;
        headpose_pitch_ = temp_headpose_pitch_mean;
        headpose_roll_ = temp_headpose_roll_mean;

        gaze_left_eye_pitch_ = temp_lefteye_gaze_pitch_mean;
        gaze_left_eye_yaw_ = temp_lefteye_gaze_yaw_mean;
        gaze_right_eye_pitch_ = temp_righteye_gaze_pitch_mean;
        gaze_right_eye_yaw_ = temp_righteye_gaze_yaw_mean;

        if (log_switch) {
            printf("DistractionWarn finally "
                   "headpose_yaw_:%f,headpose_pitch_:%f,headpose_roll_:%f,gaze_right_eye_"
                   "pitch_:%f,gaze_right_"
                   "eye_yaw_:%f,gaze_left_eye_yaw_:%f, gaze_left_eye_pitch_:%f\n",
                   headpose_yaw_, headpose_pitch_, headpose_roll_, gaze_right_eye_pitch_,
                   gaze_right_eye_yaw_, gaze_left_eye_yaw_, gaze_left_eye_pitch_);
        }

        bool eye_cali_finish = false;
        if (fusion_use_eye == 1) {
            eye_cali_finish = status.reye_cali_finish;
        } else if (fusion_use_eye == 0) {
            eye_cali_finish = status.leye_cali_finish;
        } else if (fusion_use_eye == 2) {
            eye_cali_finish = status.reye_cali_finish || status.leye_cali_finish;
        }

        if (status.head_cali_finish && eye_cali_finish) {
            auto_calibration = true;
        }

        if (!auto_calibration) {
            if (log_switch) {
                printf("DistractionWarn  自动标定中...........\n");
            }
            return CALIBRATE_DOING;
        }
        return CALIBRATE_DONE;
    } else if (auto_calibration) {
        if (log_switch) {
            printf(
                "DistractionWarn  "
                "自动标定已完成...........headpose_yaw_:%f,headpose_pitch_:%f,gaze_right_eye_pitch_"
                ":%f,gaze_right_eye_yaw_:%f ,gaze_left_eye_yaw_:%f,gaze_left_eye_pitch_:%f\n",
                headpose_yaw_, headpose_pitch_, gaze_right_eye_pitch_, gaze_right_eye_yaw_,
                gaze_left_eye_yaw_, gaze_left_eye_pitch_);
        }
        return CALIBRATE_DONE;
    } else {
        //防止中途标定一半(车速不满足，档位不满足等)，重新清理数据//累计时间窗逻辑则不需要重新清理
        // ClearCalibration();
        if (log_switch) {
            if (car_info->speed < 20)
                printf("DistractionWarn  车速不满足，不进行分神标定,使用默认参数...........\n");
            if (car_info->gear != TXGearPition::FORWARD)
                printf("DistractionWarn  档位不是前进挡，不进行分神标定,使用默认参数...........\n");
        }
        return CALIBRATE_CONDITION_UNDONE;
    }
}

void DistractionWarn::applyHeadYawBias(const TXDmsFaceInfo& face_info, long now_ts) {
    constexpr float kMaxHeadAngle = 50.0f;

    distract_param.head_yaw_bias = 0;
    bool is_mask = (face_info.isMask == 1) ? true : false;
    if (std::abs(face_info.head_pitch) < kMaxHeadAngle &&
        std::abs(face_info.head_roll) < kMaxHeadAngle && !is_mask) {
        float clamped_yaw = std::max(-50.0f, std::min(face_info.head_yaw, 50.0f));
        head_yaw_3s_vec.emplace_back(std::make_pair(now_ts, clamped_yaw));
        temp_vec.push_back(clamped_yaw);

        if (head_yaw_3s_vec.size() >= 2) {
            while ((head_yaw_3s_vec.back().first - head_yaw_3s_vec.front().first) >
                   head_yaw_bias_window_time) {
                head_yaw_3s_vec.pop_front();
                if (!temp_vec.empty())
                    temp_vec.pop_front();
            }
        }
        // printf("head_yaw_bias_window_time:%d,time_gap:%d,array_size:%d\n", head_yaw_bias_window_time,
        //        (head_yaw_3s_vec.back().first - head_yaw_3s_vec.front().first), temp_vec.size());

        // 平滑head yaw bias抑制逻辑的数据统计，减少特殊场景对报警的影响
        if (temp_vec.size() >= 6) {
            std::vector<float> sorted_vec(temp_vec.begin(), temp_vec.end());
            std::sort(sorted_vec.begin(), sorted_vec.end());

            float min1 = sorted_vec[0];
            float min2 = sorted_vec[1];
            float min3 = sorted_vec[2];

            float max1 = sorted_vec[sorted_vec.size() - 1];
            float max2 = sorted_vec[sorted_vec.size() - 2];
            float max3 = sorted_vec[sorted_vec.size() - 3];
            head_yaw_3s_min = (min2 + min3) / 2.0f;
            head_yaw_3s_max = (max2 + max3) / 2.0f;
            distract_param.head_yaw_bias = fabs(head_yaw_3s_min - head_yaw_3s_max);
        } 
    }
}

bool DistractionWarn::isSteeringAngleInvalid(const TXCarInfo* car_info,
                                             std::string& distraction_reason) {
    // 角度 = 弧度 × (180/π)
    float angle = car_info->steer_whl_snsr_rad * (180 / 3.14159);
    float max_value = std::max(std::fabs(30.0f - (0.375f * car_info->speed)), 10.0f);
    // printf("angle:%f,max_value:%f\n", angle, max_value);
    if (car_info->turn_light == TURN_ON_LEFT || car_info->turn_light == TURN_ON_RIGHT ||
        car_info->speed < 20 || fabs(angle) >= max_value) {
        // printf("Being distracted is suppressed! \n", angle, max_value);
        distraction_reason = "no1";
        return true;
    }
    return false;
}

bool DistractionWarn::isSpeGlassesSituation(const TXDmsFaceInfo& face_info) {
    bool is_spe_glasses_solution = false;
#if defined(BYD_HA6)
    bool is_glasses = false;
    bool left_gaze_visuable = false;
    bool right_gaze_visuable = false;
    if (face_info.right_eye_landmark.eye_score != 0) {  //因为有-1的值，所以得区分-1的情况
        right_gaze_visuable = true;
    } else if (face_info.left_eye_landmark.eye_score != 0) {
        left_gaze_visuable = true;
    }
    if (face_info.isGlass != 0)
        is_glasses = true;
    // 因为眼睛状态的检测不稳定，所以当一定时间内有检测到眼镜，则认为是戴眼镜状态
    glasses_vec.emplace_back(is_glasses);
    if (glasses_vec.size() >= 2) {
        for (auto glasses_status : glasses_vec) {
            if (glasses_status == true) {
                is_glasses = true;
                break;
            }
        }
        if (glasses_vec.size() >= 100) {
            glasses_vec.pop_front();
        }
    }

    if (is_glasses) {
        if (fusion_use_eye == 0) {
            is_spe_glasses_solution = !left_gaze_visuable;
        } else if (fusion_use_eye == 1) {
            is_spe_glasses_solution = !right_gaze_visuable;
        } else if (fusion_use_eye == 2) {
            is_spe_glasses_solution = (!left_gaze_visuable && !right_gaze_visuable);
        }
    }
#endif
    return is_spe_glasses_solution;
}

bool DistractionWarn::isSpeMaskSituation(const TXDmsFaceInfo& face_info) {
    bool is_spe_mask_solution = false;
    bool is_mask = false;

    if (face_info.isMask == 1)
        is_mask = true;
    // 因为眼睛状态的检测不稳定，所以当一定时间内有检测到眼镜，则认为是戴眼镜状态
    mask_vec.emplace_back(is_mask);
    if (mask_vec.size() >= 2) {
        for (auto mask_status : mask_vec) {
            if (mask_status == true) {
                is_mask = true;
                break;
            }
        }
        if (mask_vec.size() >= 30) {
            mask_vec.pop_front();
        }
    }

    if (is_mask) {
        is_spe_mask_solution = true;
    }
    return is_spe_mask_solution;
}

void DistractionWarn::calcOffsetFromSteeringAngle(const TXCarInfo* car_info,
                                                  bool auto_calibration,
                                                  float& headpose_yaw_l_offset,
                                                  float& headpose_yaw_r_offset) {
    //车辆小幅转弯逻辑

    float angle = car_info->steer_whl_snsr_rad * (180 / 3.14159);
    if (angle >= -10 && angle <= 10 && auto_calibration) {
        float unit = 7.0f / 10;  //每一度0.3
        float adjust_value;
        if (angle < 0) {
            adjust_value = floor(angle);  //向下取整

        } else {
            adjust_value = ceil(angle);  //向上取整
        }
        adjust_value = adjust_value * unit;
        // printf("adjust_value:%f\n", adjust_value);

        if (adjust_value < 0) {
            headpose_yaw_l_offset = fabs(adjust_value);
            // gaze_yaw_l_offset = fabs(adjust_value);

        } else {
            headpose_yaw_r_offset = adjust_value;
            // gaze_yaw_r_offset = adjust_value;
        }
    }
}

DistractionWarn::HeadRotationRange DistractionWarn::calcNonDistractHeadRotation(
    const TXCarInfo* car_info, const TXDmsFaceInfo& face_info) {
    HeadRotationRange head_rotation_range = {0};

    // 针对眼镜且眼睛不可见场景下做的抑制头部误报逻辑
    bool is_spe_glasses_solution = isSpeGlassesSituation(face_info);
    // 针对戴口罩场景下做的抑制头部误报逻辑
    bool is_spe_mask_solution = isSpeMaskSituation(face_info);

    if (has_head_cali) {
        float headpose_yaw_l_offset = 0.0f;
        float headpose_yaw_r_offset = 0.0f;
        // 计算方向盘角度对头部非分心范围的偏移值
        calcOffsetFromSteeringAngle(car_info, auto_calibration, headpose_yaw_l_offset,
                                    headpose_yaw_r_offset);
        float head_yaw_min = 0.0f, head_yaw_max = 0.0f, head_pitch_min = 0.0f,
              head_pitch_max = 0.0f;

        if (read_json) {
            head_yaw_min = headpose_yaw_ - headpose_yaw_left - headpose_yaw_l_offset;
            head_yaw_max = headpose_yaw_ + headpose_yaw_right + headpose_yaw_r_offset;
            head_pitch_min = headpose_pitch_ - headpose_pitch_down;
            head_pitch_max = headpose_pitch_ + headpose_pitch_up;
            if (is_spe_glasses_solution) {
                head_yaw_min = head_yaw_min;  // - headpose_spe_glasses_yaw_left;
                head_yaw_max = head_yaw_max;  // + headpose_spe_glasses_yaw_right;
                head_pitch_min = head_pitch_min - headpose_spe_glasses_pitch_down;
                head_pitch_max = head_pitch_max + headpose_spe_glasses_pitch_up;
            }
            if (is_spe_mask_solution) {
                head_yaw_min = head_yaw_min - headpose_spe_glasses_yaw_left;
                head_yaw_max = head_yaw_max + headpose_spe_glasses_yaw_right;
                head_pitch_min = head_pitch_min - headpose_spe_glasses_pitch_down;
                head_pitch_max = head_pitch_max + headpose_spe_glasses_pitch_up;
            }
        } else {
            head_yaw_min = headpose_yaw_ - HEAD_POSE_YAW_L - headpose_yaw_l_offset;
            head_yaw_max = headpose_yaw_ + HEAD_POSE_YAW_R + headpose_yaw_r_offset;
            head_pitch_min = headpose_pitch_ - HEAD_POSE_PITCH_D;
            head_pitch_max = headpose_pitch_ + HEAD_POSE_PITCH_U;
            if (is_spe_glasses_solution) {
                head_yaw_min = head_yaw_min - HEAD_POSE_SPE_GLASSES_YAW_L;
                head_yaw_max = head_yaw_max + HEAD_POSE_SPE_GLASSES_YAW_R;
                head_pitch_min = head_pitch_min - HEAD_POSE_SPE_GLASSES_PITCH_D;
                head_pitch_max = head_pitch_max + HEAD_POSE_SPE_GLASSES_PITCH_U;
            }
            if (is_spe_mask_solution) {
                head_yaw_min = head_yaw_min - HEAD_POSE_SPE_GLASSES_YAW_L;
                head_yaw_max = head_yaw_max + HEAD_POSE_SPE_GLASSES_YAW_R;
                head_pitch_min = head_pitch_min - HEAD_POSE_SPE_GLASSES_PITCH_D;
                head_pitch_max = head_pitch_max + HEAD_POSE_SPE_GLASSES_PITCH_U;
            }
        }
        distract_param.head_yaw_min = head_rotation_range.minyaw = head_yaw_min;
        distract_param.head_yaw_max = head_rotation_range.maxyaw = head_yaw_max;
        distract_param.head_pitch_min = head_rotation_range.minpitch = head_pitch_min;
        distract_param.head_pitch_max = head_rotation_range.maxpitch = head_pitch_max;
    } else {
        // std::cout << "---------2   is_spe_mask_solution:" << is_spe_mask_solution <<
        //  " " << head_rotation_range.minyaw << " " << head_rotation_range.maxyaw << " " 
        //  << head_rotation_range.minpitch << " " << head_rotation_range.maxpitch << std::endl;
        if (read_json) {
            // printf("\033[33m --hjh-- file:dms_distraction_warning_byd.cpp line:%d info:hello \033[0m \n ",__LINE__);
            head_rotation_range.minyaw = headpose_yaw_normal_min;
            head_rotation_range.maxyaw = headpose_yaw_normal_max;
            head_rotation_range.minpitch = headpose_pitch_normal_min;
            head_rotation_range.maxpitch = headpose_pitch_normal_max;
            head_rotation_range.minroll = headpose_roll_normal_min;
            head_rotation_range.maxroll = headpose_roll_normal_max;
            if (is_spe_glasses_solution) {
                head_rotation_range.minyaw = headpose_yaw_normal_min;
                head_rotation_range.maxyaw = headpose_yaw_normal_max;
                head_rotation_range.minpitch = headpose_pitch_normal_min - headpose_spe_glasses_pitch_down;
                head_rotation_range.maxpitch = headpose_pitch_normal_max + headpose_spe_glasses_pitch_up;
            }
            if (is_spe_mask_solution) {
                head_rotation_range.minyaw = headpose_yaw_normal_min - headpose_spe_glasses_yaw_left;
                head_rotation_range.maxyaw = headpose_yaw_normal_max + headpose_spe_glasses_yaw_right;
                head_rotation_range.minpitch = headpose_pitch_normal_min - headpose_spe_glasses_pitch_down;
                head_rotation_range.maxpitch = headpose_pitch_normal_max + headpose_spe_glasses_pitch_up;
            }
        } else {
            // printf("\033[33m --hjh-- file:dms_distraction_warning_byd.cpp line:%d info:hello \033[0m \n ",__LINE__);
            head_rotation_range.minyaw = HEADPOSE_YAW_NORMAL_MIN;
            head_rotation_range.maxyaw = HEADPOSE_YAW_NORMAL_MAX;
            head_rotation_range.minpitch = HEADPOSE_PITCH_NORMAL_MIN;
            head_rotation_range.maxpitch = HEADPOSE_PITCH_NORMAL_MAX;
            head_rotation_range.minroll = HEADPOSE_ROLL_NORMAL_MIN;
            head_rotation_range.maxroll = HEADPOSE_ROLL_NORMAL_MAX;
            if (is_spe_glasses_solution) {
                head_rotation_range.minyaw = head_rotation_range.minyaw;
                head_rotation_range.maxyaw = head_rotation_range.maxyaw;
                head_rotation_range.minpitch = head_rotation_range.minpitch - HEAD_POSE_SPE_GLASSES_PITCH_D;
                head_rotation_range.maxpitch = head_rotation_range.maxpitch + HEAD_POSE_SPE_GLASSES_PITCH_U;
            }
            if (is_spe_mask_solution) {
                head_rotation_range.minyaw = head_rotation_range.minyaw - HEAD_POSE_SPE_GLASSES_YAW_L;
                head_rotation_range.maxyaw = head_rotation_range.maxyaw + HEAD_POSE_SPE_GLASSES_YAW_R;
                head_rotation_range.minpitch = head_rotation_range.minpitch - HEAD_POSE_SPE_GLASSES_PITCH_D;
                head_rotation_range.maxpitch = head_rotation_range.maxpitch + HEAD_POSE_SPE_GLASSES_PITCH_U;
            }
        }
        // std::cout << "---------1   is_spe_mask_solution:" << is_spe_mask_solution <<
        //  " " << head_rotation_range.minyaw << " " << head_rotation_range.maxyaw << " " 
        //  << head_rotation_range.minpitch << " " << head_rotation_range.maxpitch << std::endl;
    }

    return head_rotation_range;
}

bool DistractionWarn::checkFalseDistraction(const TXDmsFaceInfo& face_info,
                                            const HeadRotationRange& head_rotation_range,
                                            std::string& distraction_reason) {
    //过滤仰头+张嘴误报分神逻辑
    constexpr float MOUTH_OPENING_THRESHOLD = 0.5f;
    bool is_false_distraction = false;
    if (auto_calibration && (head_rotation_range.maxpitch < face_info.head_pitch) &&
        (face_info.mouth_opening > MOUTH_OPENING_THRESHOLD)) {
        distraction_reason = "no3";
        is_false_distraction = true;
    }
    return is_false_distraction;
}

bool DistractionWarn::checkHeadPoseDistraction(const TXDmsFaceInfo& face_info,
                                               const HeadRotationRange& head_rotation_range,
                                               std::string& detailed_reason) {
    bool head_pose = false;
    detailed_reason = "";
    if (auto_calibration) {
        if (head_rotation_range.minyaw != 0 && head_rotation_range.maxyaw != 0 &&
            head_rotation_range.minpitch != 0 && head_rotation_range.maxpitch != 0) {
            // 计算椭圆参数
            headrange_center_yaw = (head_rotation_range.minyaw + head_rotation_range.maxyaw) / 2.0f;
            headrange_center_pitch =
                (head_rotation_range.minpitch + head_rotation_range.maxpitch) / 2.0f;
            headrange_a = (head_rotation_range.maxyaw - head_rotation_range.minyaw) / 2.0f;
            headrange_b = (head_rotation_range.maxpitch - head_rotation_range.minpitch) / 2.0f;
            // 椭圆方程检测
            float normalized_yaw = (face_info.head_yaw - headrange_center_yaw) / headrange_a;
            float normalized_pitch = (face_info.head_pitch - headrange_center_pitch) / headrange_b;
            float ellipse_value =
                normalized_yaw * normalized_yaw + normalized_pitch * normalized_pitch;
            // std::cout << " ellipse_value:" << ellipse_value << "center_yaw:" <<
            //     headrange_center_yaw << " center_pitch:" << headrange_center_pitch << " a:" << headrange_a << " b:" << headrange_b << std::endl;

            if (ellipse_value > 1.0f) {
                head_pose = true;
                // 判断具体超出方向
                if (face_info.head_yaw < head_rotation_range.minyaw) {
                    detailed_reason = "headyaw minlimit";
                } else if (face_info.head_yaw > head_rotation_range.maxyaw) {
                    detailed_reason = "headyaw maxlimit";
                } else if (face_info.head_pitch < head_rotation_range.minpitch) {
                    detailed_reason = "headpitch minlimit";
                } else if (face_info.head_pitch > head_rotation_range.maxpitch) {
                    detailed_reason = "headpitch maxlimit";
                } else {
                    // 在四个角落区域
                    detailed_reason = "headpose corner area";
                }
            }
        }
    } else {
        if (face_info.head_yaw < (head_rotation_range.minyaw) ||
            face_info.head_yaw > (head_rotation_range.maxyaw)) {
            head_pose = true;
        } else if ((face_info.head_pitch < (head_rotation_range.minpitch) ||
                    face_info.head_pitch > (head_rotation_range.maxpitch)) &&
                   (face_info.head_roll < (head_rotation_range.minroll) ||
                    face_info.head_roll > (head_rotation_range.maxroll))) {
            head_pose = true;
        }
    }
    return head_pose;
}

bool DistractionWarn::isFaceValid(const TXDmsFaceInfo& face_info) {
    bool is_face_valid = false;

    if (face_info.score >= kMinFaceScore)
        is_face_valid = true;

    return is_face_valid;
}

inline bool isGazeCredible(const TXEyeLandmark& eye_landmark) {
    return (eye_landmark.pupil_score > 0 && eye_landmark.eye_score > 0);
}
inline bool isModelDetEye(const TXEyeLandmark& eye_landmark) {
    return (eye_landmark.pupil_score != 0 && eye_landmark.eye_score != 0);
}
std::pair<float, float> DistractionWarn::calcFusionRotationAngle(const bool is_mask, const TXEyeLandmark& eye_landmark,
                                                                 float cur_head_yaw,
                                                                 float cur_head_pitch) {
    float fusion_yaw = 0.0f, fusion_pitch = 0.0f;
    if (isGazeCredible(eye_landmark)) {
        fusion_yaw = eye_landmark.yaw * gaze_yaw_threshold + (is_mask ? 0 : cur_head_yaw * headpose_yaw_threshold);
        fusion_pitch =
            eye_landmark.pitch * gaze_pitch_threshold + (is_mask ? 0 : cur_head_pitch * headpose_pitch_threshold);

        distract_param.current_right_gaze_vf_yaw = fusion_yaw;
        distract_param.current_right_gaze_vf_pitch = fusion_pitch;
    }
    return std::make_pair(fusion_yaw, fusion_pitch);
}

DistractionWarn::GazeDistractedType DistractionWarn::calcPreMapping(const TXDmsFaceInfo& face_info,
                                                                    float& fusion_yaw,
                                                                    float& fusion_pitch,
                                                                    float& cali_yaw,
                                                                    float& cali_pitch) {
    GazeDistractedType eye_gaze_type = GAZE_DISTRACTED_TYPE_UNDISTRACTED;
    fusion_yaw = 0.0f;
    fusion_pitch = 0.0f;
    cali_yaw = 0.0f;
    cali_pitch = 0.0f;

    if (auto_calibration) {
        bool is_leye_model_det = isModelDetEye(face_info.left_eye_landmark);
        bool is_reye_model_det = isModelDetEye(face_info.right_eye_landmark);
        GazeDistractedType leye_gaze_type =
            is_leye_model_det ? GAZE_DISTRACTED_TYPE_NONE : GAZE_DISTRACTED_TYPE_MODELNOTDET;
        GazeDistractedType reye_gaze_type =
            is_reye_model_det ? GAZE_DISTRACTED_TYPE_NONE : GAZE_DISTRACTED_TYPE_MODELNOTDET;

        if (leye_gaze_type == GAZE_DISTRACTED_TYPE_NONE) {
            bool is_leye_gaze_credible = isGazeCredible(face_info.left_eye_landmark);
            if (!is_leye_gaze_credible) {
                leye_gaze_type = GAZE_DISTRACTED_TYPE_INCREDIBLE;
            }
        }
        if (reye_gaze_type == GAZE_DISTRACTED_TYPE_NONE) {
            bool is_reye_gaze_credible = isGazeCredible(face_info.right_eye_landmark);
            if (!is_reye_gaze_credible) {
                reye_gaze_type = GAZE_DISTRACTED_TYPE_INCREDIBLE;
            }
        }
        bool is_mask = (face_info.isMask == 1) ? true : false;
        auto [leye_fusion_yaw, leye_fusion_pitch] = calcFusionRotationAngle(is_mask,
            face_info.left_eye_landmark, face_info.head_yaw, face_info.head_pitch);
        auto [reye_fusion_yaw, reye_fusion_pitch] = calcFusionRotationAngle(is_mask,
            face_info.right_eye_landmark, face_info.head_yaw, face_info.head_pitch);

        switch (fusion_use_eye) {
            case FUSE_EYE_LEFT:
                eye_gaze_type = leye_gaze_type;
                cali_yaw = temp_lefteye_gaze_yaw_mean;
                cali_pitch = temp_lefteye_gaze_pitch_mean;
                fusion_yaw = leye_fusion_yaw;
                fusion_pitch = leye_fusion_pitch;
                break;
            case FUSE_EYE_RIGHT:
                eye_gaze_type = reye_gaze_type;
                cali_yaw = temp_righteye_gaze_yaw_mean;
                cali_pitch = temp_righteye_gaze_pitch_mean;
                fusion_yaw = reye_fusion_yaw;
                fusion_pitch = reye_fusion_pitch;
                break;
            case FUSE_EYE_BOTH:
                eye_gaze_type = std::min(leye_gaze_type, reye_gaze_type);
                if ((leye_gaze_type == GAZE_DISTRACTED_TYPE_NONE && has_leye_cali) &&
                    (reye_gaze_type == GAZE_DISTRACTED_TYPE_NONE && has_reye_cali)) {
                    cali_pitch =
                        (temp_righteye_gaze_pitch_mean + temp_lefteye_gaze_pitch_mean) / 2.0f;
                    cali_yaw = (temp_righteye_gaze_yaw_mean + temp_lefteye_gaze_yaw_mean) / 2.0f;
                    fusion_pitch = (reye_fusion_pitch + leye_fusion_pitch) / 2.0f;
                    fusion_yaw = (reye_fusion_yaw + leye_fusion_yaw) / 2.0f;
                } else if (leye_gaze_type == GAZE_DISTRACTED_TYPE_NONE && has_leye_cali) {
                    cali_pitch = temp_lefteye_gaze_pitch_mean;
                    cali_yaw = temp_lefteye_gaze_yaw_mean;
                    fusion_pitch = leye_fusion_pitch;
                    fusion_yaw = leye_fusion_yaw;
                } else if (reye_gaze_type == GAZE_DISTRACTED_TYPE_NONE && has_reye_cali) {
                    cali_pitch = temp_righteye_gaze_pitch_mean;
                    cali_yaw = temp_righteye_gaze_yaw_mean;
                    fusion_pitch = reye_fusion_pitch;
                    fusion_yaw = reye_fusion_yaw;
                }
                break;
            default:
                std::cout << "not set fusion use eye..." << std::endl;
                break;
        }
        gaze_pitch_mean = cali_pitch;
        gaze_yaw_mean = cali_yaw;

        cali_pitch = ((face_info.isMask == 1) ? 0 : headpose_pitch_ * headpose_pitch_threshold) + cali_pitch * gaze_pitch_threshold;
        cali_yaw = ((face_info.isMask == 1) ? 0 : headpose_yaw_ * headpose_yaw_threshold) + cali_yaw * gaze_yaw_threshold;
    }

    return eye_gaze_type;
}

DistractionWarn::GazeDistractedType DistractionWarn::checkGazeDistraction(
    const TXDmsFaceInfo& face_info, bool is_curve) {
    GazeDistractedType gaze_result = GAZE_DISTRACTED_TYPE_UNDISTRACTED;
    predict_result = 0;
    mapping_x = 0.0f;
    mapping_y = 0.0f;

    if (auto_calibration) {
        float mapping_pitch = 0.0f;
        float mapping_yaw = 0.0f;
        cv::Point2f mapping_point(0.0f, 0.0f);
        double distance_to_hull = 0.0;
        float cali_pitch = 0.0f;
        float cali_yaw = 0.0f;

        gaze_result = calcPreMapping(face_info, mapping_yaw, mapping_pitch, cali_yaw, cali_pitch);

        // 投射点计算视线区域初始化
        if (region_init_flag == false) {
            if (CcSvmModel::getInstance()->init(region_mapping_width, region_mapping_height,
                                                region_hulls, tolerate_percentage) == 0) {
                CcSvmModel::getInstance()->set_cali_angle3d(cali_pitch, cali_yaw, headpose_roll_);
                region_init_flag = true;
            }
        } else {
            // 标定成功后一直进行标定修正
            if (is_loop_calibration) {
                CcSvmModel::getInstance()->set_cali_angle3d(cali_pitch, cali_yaw, headpose_roll_);
            }
            // 初始化已完成且所要求的眼睛可视
            if (gaze_result == GAZE_DISTRACTED_TYPE_NONE) {
                predict_result = CcSvmModel::getInstance()->predict(
                    mapping_pitch, mapping_yaw, headpose_roll_, 0, mapping_point,
                    distance_to_hull);  //0是index
                // 根据返回的区域索引来决定是否分神
                if (predict_result != 0) {
                    gaze_result = GAZE_DISTRACTED_TYPE_DISTRACTED;
                } else {
                    // std::cout << "distance_to_hull:" << distance_to_hull << " is_curve:" << is_curve
                    //           << " mapping_point.y:" << mapping_point.y << std::endl;
                    // if (distance_to_hull < 15.0 && !is_curve && mapping_point.y > 405) {
                    //     gaze_result = GAZE_DISTRACTED_TYPE_DISTRACTED_WITH_NOTCURVE;
                    // } else {
                    gaze_result = GAZE_DISTRACTED_TYPE_UNDISTRACTED;
                    // // }
                }
            }

            mapping_x = mapping_point.x;
            mapping_y = mapping_point.y;
        }
    }

    return gaze_result;
}

bool DistractionWarn::distractionProcess(const TXDmsFaceInfo& face_info,
                                         bool is_headpose_distracted,
                                         GazeDistractedType gaze_result,
                                         std::string& distraction_reason,
                                         const float right_up_down_proportion,
                                         const float left_up_down_proportion) {
    bool current_distraction_status = false;
    if (auto_calibration) {
        // 双眼都可视时，优先使用眼睛判断分心
        bool both_eyes_credible = isGazeCredible(face_info.left_eye_landmark) &&
            isGazeCredible(face_info.right_eye_landmark);
        if (both_eyes_credible && gaze_result == GAZE_DISTRACTED_TYPE_DISTRACTED) {
            current_distraction_status = true;
            distraction_reason = distraction_reason + "eyegaze";
        }
        // 增加眼睛曲率判断
        if (gaze_result == GAZE_DISTRACTED_TYPE_DISTRACTED_WITH_NOTCURVE) {
            current_distraction_status = true;
            distraction_reason = distraction_reason + "eyenotcurve";
        }

        if (!current_distraction_status) {
            bool is_mask = (face_info.isMask == 1) ? true : false;
            if (face_info.head_yaw > std::min(headgaze_yaw_l_offset, headgaze_yaw_r_offset) + ((is_mask) ? HEAD_POSE_SPE_GLASSES_YAW_L : 0) &&
                face_info.head_yaw < std::max(headgaze_yaw_l_offset, headgaze_yaw_r_offset) + ((is_mask) ? HEAD_POSE_SPE_GLASSES_YAW_R : 0)) {
                if (gaze_result == GAZE_DISTRACTED_TYPE_DISTRACTED) {
                    current_distraction_status = true;
                    distraction_reason = distraction_reason + "eyegaze";
                } else if (gaze_result == GAZE_DISTRACTED_TYPE_MODELNOTDET &&
                           is_headpose_distracted) {
                    current_distraction_status = true;
                    distraction_reason = distraction_reason + "headpose";
                }

                // //新增视线往下看不报分心逻辑
                // if (current_distraction_status == false) {
                //     if (face_info.right_eye_landmark.eye_score > 0 &&
                //         face_info.right_eye_landmark.opening >= kMinEyeOpening) {
                //         if (right_up_down_proportion < righteye_up_down_proportion) {
                //             current_distraction_status = true;
                //             distraction_reason = distraction_reason + "right_eye_gaze2";
                //         }
                //     }

                //     if (face_info.left_eye_landmark.eye_score > 0 &&
                //         face_info.left_eye_landmark.opening >= kMinEyeOpening) {
                //         if (left_up_down_proportion < lefteye_up_down_proportion) {
                //             current_distraction_status = true;
                //             distraction_reason = distraction_reason + "left_eye_gaze2";
                //         }
                //     }
                // }
            } else {
                if (is_headpose_distracted) {
                    current_distraction_status = true;
                    distraction_reason = distraction_reason + "headpose";
                }
            }
        }
    } else {
        if (is_headpose_distracted) {
            current_distraction_status = true;
            distraction_reason = distraction_reason + "headpose";
        }
    }

    // 增加上一次分心原因记录
    last_distraction_reason = "";
    if (current_distraction_status) {
        if (distraction_reason.find("eyegaze") != std::string::npos) {
            last_distraction_reason = std::to_string(fusion_use_eye) + "_" + distraction_reason;
            if (mapping_x >= 400) {
                if (mapping_y >= 410)
                    last_distraction_reason += "right_lower";  //"left_upper";
                else
                    last_distraction_reason += "right_upper";  //"left_lower";
            } else if (mapping_x < 400) {
                if (mapping_y >= 410)
                    last_distraction_reason += "left_lower";  //"right_upper";
                else
                    last_distraction_reason += "left_upper";  //"right_lower";
            }
        }
        // std::cout << "last_distraction_reason:" << last_distraction_reason << std::endl;
    }

    return current_distraction_status;
}

void DistractionWarn::checkAbnormalDistraction(TXDmsFaceInfo& face_info,
                                               bool& current_distraction_status,
                                               std::string& headpose_reason,
                                               std::string& distraction_reason,
                                               float right_pupil_to_up_down_dis,
                                               float left_pupil_to_up_down_dis) {
    GazeDistractedType gaze_result = GAZE_DISTRACTED_TYPE_NONE;
    // 1.在脸转向摄像头侧时，近摄像头眼睛的瞳孔的像素移动会造成更大的gaze yaw differ,所以给一个debuff后再计算mapping情况
    if ((headpose_reason.find("headyaw minlimit") != std::string::npos &&
         fusion_use_eye == FUSE_EYE_RIGHT) ||
        (headpose_reason.find("headyaw maxlimit") != std::string::npos &&
         fusion_use_eye == FUSE_EYE_LEFT)) {
        if (fusion_use_eye == FUSE_EYE_RIGHT &&
            face_info.right_eye_landmark.yaw - gaze_yaw_mean >
                0) {  // 异常姿态给一个矫正，但不能小于标定值
            face_info.head_yaw = std::max(float(face_info.head_yaw * 0.7), headpose_yaw_);
        } else if (fusion_use_eye == FUSE_EYE_LEFT &&
                   face_info.left_eye_landmark.yaw - gaze_yaw_mean < 0) {  // 这个大于还是小于待确认
            face_info.head_yaw = std::max(float(face_info.head_yaw * 0.7), headpose_yaw_);
        }

        gaze_result = checkGazeDistraction(face_info);
        if (gaze_result == GAZE_DISTRACTED_TYPE_UNDISTRACTED) {
            current_distraction_status = false;
            distraction_reason = distraction_reason + " eyegaze_abnormal_1";
        }
    }
    // 2.低头且看前方驾驶时，需要减弱头部pitch的影响
    if (current_distraction_status &&
        headpose_reason.find("headpitch minlimit") != std::string::npos) {
        //TODO:因为个体差异，opening值是否也弄一个标定得到的值？
        if (fusion_use_eye == FUSE_EYE_RIGHT) {
            if (right_pupil_to_up_down_dis < 0.45 && face_info.right_eye_landmark.opening > 0.45 &&
                (headpose_reason.find("headyaw minlimit") == std::string::npos &&
                 headpose_reason.find("headyaw maxlimit") == std::string::npos)) {
                float pitch_diff = face_info.head_pitch - headpose_pitch_;
                face_info.head_pitch = headpose_pitch_ + pitch_diff * 0.5;
            } else if (face_info.right_eye_landmark.pitch - gaze_pitch_mean > -0.5) {
                face_info.head_pitch = std::max(float(face_info.head_pitch * 1.3), headpose_pitch_);
            }
        } else if (fusion_use_eye == FUSE_EYE_LEFT) {
            if (left_pupil_to_up_down_dis < 0.45 && face_info.left_eye_landmark.opening > 0.45 &&
                (headpose_reason.find("headyaw minlimit") == std::string::npos &&
                 headpose_reason.find("headyaw maxlimit") == std::string::npos)) {
                float pitch_diff = face_info.head_pitch - headpose_pitch_;
                face_info.head_pitch = face_info.head_pitch + pitch_diff * 0.5;
            } else if (face_info.left_eye_landmark.pitch - gaze_pitch_mean > -0.5) {
                face_info.head_pitch = std::max(float(face_info.head_pitch * 1.3), headpose_pitch_);
            }
        }
        if (current_distraction_status) {
            gaze_result = checkGazeDistraction(face_info);
            if (gaze_result == GAZE_DISTRACTED_TYPE_UNDISTRACTED) {
                current_distraction_status = false;
                distraction_reason = distraction_reason + " eyegaze_abnormal_2";
            }
        }
    }
    // 3.偏头且看前方驾驶时，需要减弱头部pitch的影响
    if (current_distraction_status && std::abs(face_info.head_roll) >= 25) {
        face_info.head_pitch = std::max(float(face_info.head_pitch * 0.7), headpose_pitch_);
        gaze_result = checkGazeDistraction(face_info);
        if (gaze_result == GAZE_DISTRACTED_TYPE_UNDISTRACTED) {
            current_distraction_status = false;
            distraction_reason = distraction_reason + " eyegaze_abnormal_3";
        }
    }
}

void DistractionWarn::keepHistoryEyeGaze(TXEyeLandmark& eye_info,
                                         TXEyeLandmark& last_eye_landmark,
                                         int& eye_lost_count,
                                         int eye_flag) {
    // 眼睛瞳孔分数为0，且没有连续3帧为0，则使用上一帧保存的数据
    if (eye_info.pupil_score == 0) {  // 改为等于0，因为不包括模型输出后被一系列逻辑改成小于0的
        bool keep_flag = false;  // 对使用历史视线数据增加更精确的条件
        if (last_distraction_reason.find("eyegaze") != std::string::npos &&
            last_distraction_reason.find("lower") != std::string::npos) {
            if ((eye_flag == FUSE_EYE_LEFT &&
                 last_distraction_reason.find("0") != std::string::npos) ||
                (eye_flag == FUSE_EYE_RIGHT &&
                 last_distraction_reason.find("1") != std::string::npos)) {
                keep_flag = true;
            }
        }
        if (keep_flag && ++eye_lost_count <= 2) {
            eye_info = last_eye_landmark;
            eye_info.opening = 0.0f;
        }
    } else {
        last_eye_landmark = eye_info;
        eye_lost_count = 0;
    }
}

bool DistractionWarn::isEyeCurve(const TXDmsFaceInfo face_info,
                                 const distra_related_eyeinfo& related_eyeinfo) {
    if (!auto_calibration)
        return true;
    
    if (mapping_y-400 < kMinMappingYDiffer)
        return true;

    float roll_diff = std::abs(face_info.head_roll - headpose_roll_);
    float pitch_diff = std::abs(face_info.head_pitch - headpose_pitch_);
    float yaw_diff = std::abs(face_info.head_yaw - headpose_yaw_);

    if (roll_diff >= kMinRollDiffer || pitch_diff >= kMinPitchDiffer || yaw_diff >= kMinYawDiffer)
        return true;

    bool is_curve = true;
    float curve_score = 1.0f;
    float curve_score_mean_ = 1.0f;
    float offset_value = -1.0f;  //0-1
    float eye_pitch_offset = -1.0f;
    float offset_base = (1 - kCurveThreshold) * (KMaxCurveScore / 2);  //0.0003
    // 防止出现标定出极端值的情况
    if (leye_uper_curve_score_mean_ > KMaxCurveScore) {
        leye_uper_curve_score_mean_ = KMaxCurveScore;
    }
    if (reye_uper_curve_score_mean_ > KMaxCurveScore) {
        reye_uper_curve_score_mean_ = KMaxCurveScore;
    }

    curve_score = related_eyeinfo.leye_coutour_upper_curve_score;
    curve_score_mean_ = leye_uper_curve_score_mean_;
    eye_pitch_offset = (auto_calibration)
        ? (gaze_left_eye_pitch_ - face_info.left_eye_landmark.pitch - KEyePitchOffsetBase)
        : 0;
    if (curve_score < curve_score_mean_ && eye_pitch_offset > 0) {
        if (eye_pitch_offset >= KEyePitchMaxOffset) {
            offset_value = 1.0f;
        } else if (eye_pitch_offset < KEyePitchMaxOffset) {
            offset_value = eye_pitch_offset / KEyePitchMaxOffset;
        }
    }

    if (offset_value > 0 && (isGazeCredible(face_info.left_eye_landmark))) {
        if (face_info.left_eye_landmark.opening > kMinEyeOpening && curve_score > KMinCurveScore) {
            curve_score -= offset_base * offset_value;
            if (curve_score < curve_score_mean_ * kCurveThreshold)  // 0.0025*0.2 = 0.0005
                is_curve = false;
        }
    }
    if (is_curve) {
        curve_score = related_eyeinfo.reye_coutour_upper_curve_score;
        curve_score_mean_ = reye_uper_curve_score_mean_;
        offset_value = -1.0f;
        eye_pitch_offset = -1.0f;

        eye_pitch_offset = (auto_calibration)
            ? (gaze_right_eye_pitch_ - face_info.right_eye_landmark.pitch - KEyePitchOffsetBase)
            : 0;
        if (curve_score < curve_score_mean_ && eye_pitch_offset > 0) {
            if (eye_pitch_offset >= KEyePitchMaxOffset) {
                offset_value = 1.0f;
            } else if (eye_pitch_offset < KEyePitchMaxOffset) {
                offset_value = eye_pitch_offset / KEyePitchMaxOffset;
            }
        }
        if (offset_value > 0 && (isGazeCredible(face_info.right_eye_landmark))) {
            if (face_info.right_eye_landmark.opening > kMinEyeOpening &&
                curve_score > KMinCurveScore) {
                curve_score -= offset_base * offset_value;
                if (curve_score < curve_score_mean_ * kCurveThreshold)
                    is_curve = false;
            }
        }
    }
    // switch (fusion_use_eye) {
    //     case FUSE_EYE_LEFT:
    //         // 左眼融合模式：检查左眼状态
    //         if (face_info.left_eye_landmark.eye_score > 0 && face_info.left_eye_landmark.pupil_score <= 0
    //         && face_info.left_eye_landmark.opening > 0.1) {
    //             if (related_eyeinfo.leye_coutour_upper_curve_score < KMinCurveScore)
    //                 is_curve = false;
    //         }
    //         break;
    //     case FUSE_EYE_RIGHT:
    //         // 右眼融合模式：检查右眼状态
    //         if (face_info.right_eye_landmark.eye_score > 0 && face_info.right_eye_landmark.pupil_score <= 0
    //         && face_info.right_eye_landmark.opening > 0.1) {
    //             printf("\033[33m --hjh-- file:dms_distraction_warning_byd.cpp line:%d info:hello \033[0m \n ",__LINE__);
    //             if (related_eyeinfo.reye_coutour_upper_curve_score < KMinCurveScore)
    //                 is_curve = false;
    //         }
    //         break;
    //     case FUSE_EYE_BOTH: {
    //         // 双眼融合模式：检查双眼状态，任一眼满足条件即认为不是曲线
    //         bool left_eye_not_curve = (face_info.left_eye_landmark.eye_score > 0 &&
    //                                  face_info.left_eye_landmark.pupil_score <= 0 &&
    //                                  related_eyeinfo.leye_coutour_upper_curve_score < KMinCurveScore);
    //         bool right_eye_not_curve = (face_info.right_eye_landmark.eye_score > 0 &&
    //                                   face_info.right_eye_landmark.pupil_score <= 0 &&
    //                                   related_eyeinfo.reye_coutour_upper_curve_score < KMinCurveScore);
    //         if (left_eye_not_curve || right_eye_not_curve) {
    //             is_curve = false;
    //         }
    //         break;
    //     }
    //     default:
    //         break;
    // }
    bool left_eye_not_curve = (face_info.left_eye_landmark.eye_score > 0 &&
                               face_info.left_eye_landmark.pupil_score <= 0 &&
                               related_eyeinfo.leye_coutour_upper_curve_score < KMinCurveScore
                               && face_info.left_eye_landmark.opening > 0.1);
    bool right_eye_not_curve = (face_info.right_eye_landmark.eye_score > 0 &&
                                face_info.right_eye_landmark.pupil_score <= 0 &&
                                related_eyeinfo.reye_coutour_upper_curve_score < KMinCurveScore
                                && face_info.right_eye_landmark.opening > 0.1);
    if (left_eye_not_curve || right_eye_not_curve) {
        is_curve = false;
    }
    // std::cout << "curve_score:" << curve_score
    //           << " curve_score_thr:" << curve_score_mean_ * 0.88 << std::endl;
    return is_curve;
}

bool DistractionWarn::IsDistracted(TXDmsFaceInfo face_info,
                                   const TXCarInfo* car_info,
                                   const distra_related_eyeinfo& related_eyeinfo,
                                   bool is_face_keypoints_valid,
                                   long now_ts) {
    current_distraction_status = false;
    distraction_reason = "";
    bool is_face_valid = isFaceValid(face_info);
    // std::cout << "is_face_valid:" << is_face_valid << " is_face_keypoints_valid:" << is_face_keypoints_valid << std::endl;

    if (is_face_valid && is_face_keypoints_valid) {
        // 解耦将上一层的处理移入分心模块
        keepHistoryEyeGaze(face_info.left_eye_landmark, last_left_eye_landmark, left_eye_lost_count,
                           FUSE_EYE_LEFT);
        keepHistoryEyeGaze(face_info.right_eye_landmark, last_right_eye_landmark,
                           right_eye_lost_count, FUSE_EYE_RIGHT);

        applyHeadYawBias(face_info, now_ts);
        if (isSteeringAngleInvalid(car_info, distraction_reason)) {
            return false;
        }

        HeadRotationRange head_rotation_range = {0};
        head_rotation_range = calcNonDistractHeadRotation(car_info, face_info);

        if (checkFalseDistraction(face_info, head_rotation_range, distraction_reason)) {
            return false;
        }
        headpose_reason = "";
        bool is_headpose_distracted =
            checkHeadPoseDistraction(face_info, head_rotation_range, headpose_reason);

        bool is_curve = true;
        auto gaze_result = checkGazeDistraction(face_info, is_curve);
        is_curve = isEyeCurve(face_info, related_eyeinfo);
        if (gaze_result == GAZE_DISTRACTED_TYPE_UNDISTRACTED && !is_curve) {
            gaze_result = GAZE_DISTRACTED_TYPE_DISTRACTED_WITH_NOTCURVE;
        }
        // std::cout << "is_curve:" << is_curve << "gaze_result:" << gaze_result << std::endl;
        current_distraction_status = distractionProcess(
            face_info, is_headpose_distracted, gaze_result, distraction_reason,
            related_eyeinfo.right_up_down_proportion, related_eyeinfo.left_up_down_proportion);

        // 增加异常姿态的分心误检抑制
        if (current_distraction_status && distraction_reason.find("eyegaze") != std::string::npos) {
            checkAbnormalDistraction(face_info, current_distraction_status, headpose_reason,
                                     distraction_reason, related_eyeinfo.right_pupil_to_up_down_dis,
                                     related_eyeinfo.left_pupil_to_up_down_dis);
        }

    } else {
//         if (!is_face_valid) {
// #if defined(BYD_EQ)
//             distraction_reason = distraction_reason + "no_face_det";
//             current_distraction_status = true;
// #endif
//         }
    }

    if (!current_distraction_status &&
        distraction_reason.find("eyegaze_abnormal") == std::string::npos) {
        distraction_reason = "no_distraction";
    }
    // std::cout << "2 current_distraction_status:" << current_distraction_status << "distraction_reason:" << distraction_reason << std::endl;
    return current_distraction_status;
}

void DistractionWarn::StartCalibration(const bool is_mask, 
                                       float head_pose_yaw,
                                       float head_pose_pitch,
                                       float head_pose_roll,
                                       float gaze_left_yaw,
                                       float gaze_left_pitch,
                                       float gaze_right_yaw,
                                       float gaze_right_pitch,
                                       float left_eye_conf,
                                       float right_eye_conf,
                                       const float leye_uper_curve_score,
                                       const float reye_uper_curve_score) {

    if (left_eye_conf < 1) {
        gaze_left_yaw = -9999.0f;
        gaze_left_pitch = -9999.0f;
    }
    if (right_eye_conf < 1) {
        gaze_right_yaw = -9999.0f;
        gaze_right_pitch = -9999.0f;
    }

    if (is_mask)
        calibrator->changeparam(8.0);
    else
        calibrator->changeparam(5.0);

    std::vector<float> head_angles = {head_pose_pitch, head_pose_yaw, head_pose_roll};
    std::vector<float> lefteye_angles = {gaze_left_pitch, gaze_left_yaw / 10};
    std::vector<float> righteye_angles = {gaze_right_pitch, gaze_right_yaw / 10};
    std::vector<float> centroid;
    std::vector<float> leyecentroid;
    std::vector<float> reyecentroid;
    float leye_uper_curve_score_mean = 1.0f;
    float reye_uper_curve_score_mean = 1.0f;

    if (calibrator->execute(head_angles, lefteye_angles, righteye_angles, leye_uper_curve_score,
                            reye_uper_curve_score, centroid, leyecentroid, reyecentroid,
                            leye_uper_curve_score_mean, reye_uper_curve_score_mean, status)) {
        if (status.head_cali_finish) {
            has_head_cali = true;
            temp_headpose_yaw_mean = centroid[1];
            temp_headpose_pitch_mean = centroid[0];
            temp_headpose_roll_mean = centroid[2];
        }
        if (status.leye_cali_finish) {
            has_leye_cali = true;
            temp_lefteye_gaze_pitch_mean = leyecentroid[0];
            temp_lefteye_gaze_yaw_mean = leyecentroid[1] * 10;
            if (is_leye_first_cali_success) {
                is_leye_first_cali_success = false;
                leye_uper_curve_score_mean_ = leye_uper_curve_score_mean;
                std::cout << "leye_uper_curve_score_mean_:" << leye_uper_curve_score_mean_
                          << std::endl;
            }
        }
        if (status.reye_cali_finish) {
            has_reye_cali = true;
            temp_righteye_gaze_pitch_mean = reyecentroid[0];
            temp_righteye_gaze_yaw_mean = reyecentroid[1] * 10;
            if (is_reye_first_cali_success) {
                is_reye_first_cali_success = false;
                reye_uper_curve_score_mean_ = reye_uper_curve_score_mean;
                std::cout << "reye_uper_curve_score_mean_:" << reye_uper_curve_score_mean_
                          << std::endl;
            }
        }
    }

    return;
}

void DistractionWarn::ClearCalibration() {
    auto_calibration = false;
    has_head_cali = false;
    has_leye_cali = false;
    has_reye_cali = false;
    headpose_yaw_ = 0.0f;
    headpose_pitch_ = 0.0f;
    headpose_roll_ = 0.0f;
    gaze_left_eye_yaw_ = 0.0f;
    gaze_right_eye_yaw_ = 0.0f;

    head_yaw_3s_vec.clear();
    temp_vec.clear();

    calibrator->clear();
    CcSvmModel::getInstance()->clear();

    region_init_flag = false;
}

void DistractionWarn::QuickFillingCaliData() {
    static bool is_data_loaded = false;
    std::string config_file = "calidata.json";
    if (!is_data_loaded && access(config_file.c_str(), F_OK) == 0) {
        Json::Reader json_reader;
        Json::Value root;
        std::ifstream infile(config_file, std::ios::binary);
        if (!infile.is_open()) {
            std::cout << "Open  sight config file failed!" << std::endl;
        }

        if (!json_reader.parse(infile, root)) {
            std::cout << "Parse  sight json config file failed!" << std::endl;
        } else {
            is_data_loaded = true;
            std::vector<float> file_head_angles = {root["head_pitch"].asFloat(),
                                                   root["head_yaw"].asFloat(),
                                                   root["head_roll"].asFloat()};
            std::vector<float> file_lefteye_angles = {root["left_eye_pitch"].asFloat(),
                                                      root["left_eye_yaw"].asFloat() / 10};
            std::vector<float> file_righteye_angles = {root["right_eye_pitch"].asFloat(),
                                                       root["right_eye_yaw"].asFloat() / 10};
            float file_leye_curve_mean = root["left_eye_curve_mean"].asFloat();
            float file_reye_curve_mean = root["right_eye_curve_mean"].asFloat();

            calibrator->quickFilling(file_head_angles, file_lefteye_angles, file_righteye_angles,
                                     file_leye_curve_mean, file_reye_curve_mean);

            std::cout << "Cali data filling ok!" << std::endl;
            std::cout << "Head angles(yaw/pitch/roll): " << file_head_angles[1] << ", "
                      << file_head_angles[0] << ", " << file_head_angles[2] << std::endl;
            std::cout << "Left eye angles(yaw/pitch): " << file_lefteye_angles[1] * 10 << ", "
                      << file_lefteye_angles[0] << std::endl;
            std::cout << "Right eye angles(yaw/pitch): " << file_righteye_angles[1] * 10 << ", "
                      << file_righteye_angles[0] << std::endl;
            std::cout << "Left eye curve score:" << file_leye_curve_mean << std::endl;
            std::cout << "Right eye curve score:" << file_reye_curve_mean << std::endl;
        }
    }

    return;
}

bool DistractionWarn::GetCalibrationStatus() {
    return auto_calibration;
}

void DistractionWarn::GetHeadPosePitch(float& min_value, float& max_value) {
    if (read_json) {
        min_value = headpose_pitch_ - pitch_down;
        max_value = headpose_pitch_ + pitch_up;
    } else {
        min_value = headpose_pitch_ - PITCH_DOWN;
        max_value = headpose_pitch_ + PITCH_UP;
    }
    return;
}

void DistractionWarn::GetHeadPoseYaw(float& min_value, float& max_value) {
    if (read_json) {
        min_value = headpose_yaw_ - yaw_left;
        max_value = headpose_yaw_ + yaw_right;
    } else {
        min_value = headpose_yaw_ - YAW_LEFT;
        max_value = headpose_yaw_ + YAW_RIGHT;
    }
    return;
}

void DistractionWarn::GetHeadPoseRoll(float& min_value, float& max_value) {
    if (read_json) {
        min_value = headpose_roll_ - roll_left;
        max_value = headpose_roll_ + roll_right;
    } else {
        min_value = headpose_roll_ - ROLL_LIFT;
        max_value = headpose_roll_ + ROLL_RIGHT;
    }
    return;
}

void DistractionWarn::getNonDistractHeadRotation(float& headyaw_min,
                                                 float& headyaw_max,
                                                 float& headpitch_min,
                                                 float& headpitch_max) {
    if (auto_calibration) {
        headyaw_min = distract_param.head_yaw_min;
        headyaw_max = distract_param.head_yaw_max;
        headpitch_min = distract_param.head_pitch_min;
        headpitch_max = distract_param.head_pitch_max;

    } else {
        headyaw_min = -90;
        headyaw_max = 90;
        headpitch_min = -90;
        headpitch_max = 90;
    }
}

std::string DistractionWarn::GetDistractParamers() {
    std::stringstream ss;
    ss << "{";
    ss << "\"head_yaw_min\":" << distract_param.head_yaw_min << ",";
    ss << "\"head_yaw_max\":" << distract_param.head_yaw_max << ",";
    ss << "\"head_pitch_min\":" << distract_param.head_pitch_min << ",";
    ss << "\"head_pitch_max\":" << distract_param.head_pitch_max << ",";
    // ss << "\"right_gaze_vf_yaw_min\":" << distract_param.right_gaze_vf_yaw_min << ",";
    // ss << "\"right_gaze_vf_yaw_max\":" << distract_param.right_gaze_vf_yaw_max << ",";
    // ss << "\"right_gaze_vf_pitch_min\":" << distract_param.right_gaze_vf_pitch_min << ",";
    // ss << "\"right_gaze_vf_pitch_max\":" << distract_param.right_gaze_vf_pitch_max << ",";

    // ss << "\"left_gaze_vf_yaw_min\":" << distract_param.left_gaze_vf_yaw_min << ",";
    // ss << "\"left_gaze_vf_yaw_max\":" << distract_param.left_gaze_vf_yaw_max << ",";
    // ss << "\"left_gaze_vf_pitch_min\":" << distract_param.left_gaze_vf_pitch_min << ",";
    // ss << "\"left_gaze_vf_pitch_max\":" << distract_param.left_gaze_vf_pitch_max << ",";
    ss << "\"headpose_pitch_\":" << headpose_pitch_ << ",";
    ss << "\"headpose_yaw_\":" << headpose_yaw_ << ",";
    ss << "\"headpose_roll_\":" << headpose_roll_ << ",";
    ss << "\"gaze_pitch_mean\":" << gaze_pitch_mean << ",";
    ss << "\"gaze_yaw_mean\":" << gaze_yaw_mean << ",";

    ss << "\"predict_result\":" << predict_result << ",";
    ss << "\"mapping_x\":" << mapping_x << ",";
    ss << "\"mapping_y\":" << mapping_y << ",";

    ss << "\"current_head_yaw_bias\":" << distract_param.head_yaw_bias << ",";

    ss << "\"current_right_gaze_vf_yaw\":" << distract_param.current_right_gaze_vf_yaw << ",";
    ss << "\"current_right_gaze_vf_pitch\":" << distract_param.current_right_gaze_vf_pitch << ",";
    ss << "\"current_left_gaze_vf_yaw\":" << distract_param.current_left_gaze_vf_yaw << ",";
    ss << "\"current_left_gaze_vf_pitch\":" << distract_param.current_left_gaze_vf_pitch << ",";
    ss << "\"leye_uper_curve_score_mean_\":" << leye_uper_curve_score_mean_ << ",";
    ss << "\"reye_uper_curve_score_mean_\":" << reye_uper_curve_score_mean_ << ",";

    ss << "\"headrange_center_yaw\":" << headrange_center_yaw << ",";
    ss << "\"headrange_center_pitch\":" << headrange_center_pitch << ",";
    ss << "\"headrange_a\":" << headrange_a << ",";
    ss << "\"headrange_b\":" << headrange_b << ",";

    // 添加 region_hulls
    ss << "\"region_hulls\":[";
    for (size_t i = 0; i < region_hulls.size(); ++i) {
        ss << "[";
        for (size_t j = 0; j < region_hulls[i].size(); ++j) {
            ss << "{\"x\":" << region_hulls[i][j].x << ",\"y\":" << region_hulls[i][j].y << "}";
            if (j < region_hulls[i].size() - 1) {
                ss << ",";
            }
        }
        ss << "]";
        if (i < region_hulls.size() - 1) {
            ss << ",";
        }
    }
    ss << "]";

    ss << "}";
    return ss.str();
}

void DistractionWarn::GetDistractionInfo(internal_analysis_distraction_info& info) {
    info = distraction_info;
}

}  // namespace tongxing
