#ifndef _DMS_OUT_WARNING_H_
#define _DMS_OUT_WARNING_H_
#include <chrono>
#include <map>
#include <vector>
#include "dms_calculate_warning_byd.h"
#include "dms_distraction_warning_byd.h"
#include "tx_dms_sdk.h"

namespace tongxing {

typedef struct TXModelOut_ {
    float mouth_open;
    float l_eye_close;
    float r_eye_close;
    float face_angle_x;
    float face_angle_y;
    float face_angle_z;
} TXModelOut;

class dms_out_warning {
  private:
    /* data */
    // dms_calculate_warning *eye_datect_time_1;
    // dms_calculate_warning *eye_datect_time_2;
    // dms_calculate_warning *mouth_datect;

    DrowsinessWarn* Dwarn;
    DistractionWarn* Distrac;

    float l_eye_cloes_threshold = 0.7;
    float r_eye_cloes_threshold = 0.7;
    float yawn_opne_threshold = 0.5;
    static constexpr float MouthOpenThrUp = 0.6f;    // 张嘴闭合度的最小阈值
    static constexpr float MouthOpenThrDown = 0.3f;  // 张嘴到闭嘴之间的中间态阈值

    int output_model_bool(const TXModelOut* model_out,
                          Warn_Info& warn_info_,
                          TXDmsFaceInfo face_info);

  public:
    dms_out_warning(/* args */);
    ~dms_out_warning();
    int dms_init_warning();
    int DrowsiRestAlarm();
    int DmsRestEyeAlarm();
    int DmsAlarmSetOk();
    TXDrowsinessType drowsi_run_warning(const long time,
                                        TXDmsFaceInfo face_info,
                                        const TXCarInfo* car_info,
                                        const TXModelOut* input,
                                        TXWarnInfo& warn_info_out);

    TXDistractionType distrac_run_warning(const long time, const TXCarInfo* car_info, bool status);
    int DistracRestAlarm();
    void ResetCalibration();      //重置分神标定
    bool GetCalibrationStatus();  //获取标定状态
    TXDistracCaliStatus auto_calibration_distraction(TXDmsFaceInfo face_info,
                                                     const TXCarInfo* car_info,
                                                     const float leye_uper_curve_score,
                                                     const float reye_uper_curve_score,
                                                     long now_ts);  //自动标定
    bool GetCurrentDistractStatus(TXDmsFaceInfo face_info,
                                  const TXCarInfo* car_info,
                                  const distra_related_eyeinfo& related_eyeinfo,
                                  bool is_face_keypoints_valid,
                                  long now_ts);  //获取当前是否分神
    bool IsNoFaceMixDistraction(bool isFaceValid,
                                const float face_keypoints_score,
                                const TXCarInfo* car_info,  
                                bool camera_occlusion,
                                bool distraction_status,
                                long now_ts);
    void getNonDistractHeadRotation(float& headyaw_min,
                                    float& headyaw_max,
                                    float& headpitch_min,
                                    float& headpitch_max);
    void GetHeadPosePitch(float& min_value,
                          float& max_value);  //获取标定后headpose pitch最小角度值
    void GetHeadPoseRoll(float& min_value, float& max_value);  //获取标定后headpose roll角度值
    void GetHeadPoseYaw(float& min_value, float& max_value);

    std::string GetDistractReason();
    std::string GetDistractParamers();

    void GetTiredInfo(tx_tired& tired_info);
    void GetDistractionInfo(internal_analysis_distraction_info& info);
};

}  // namespace tongxing
#endif